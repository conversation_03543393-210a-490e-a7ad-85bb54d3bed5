<style>
    .total {
        font-size: 1.5rem;
        font-weight: bold;
        color: #a2b23a;
    }

    .checkbox-group label {
        font-size: 0.9rem;
    }
</style>

<!-- Standard Login Modal -->
<div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-3" id="loginModalLabel">{{ currentLangLabels.login_in }}</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Tabs for login/register -->
                <ul class="nav nav-tabs mb-4" id="loginModalTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="login-modal-tab" data-bs-toggle="tab"
                                data-bs-target="#login-modal" type="button" role="tab">
                            {{ currentLangLabels.login_client }}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="register-modal-tab" data-bs-toggle="tab"
                                data-bs-target="#register-modal" type="button" role="tab">
                            {{currentLangLabels.login_newclient}}
                        </button>
                    </li>
                </ul>

                <!-- Tab content -->
                <div class="tab-content" id="loginModalTabsContent">
                    <!-- Login tab -->
                    <div class="tab-pane fade show active" id="login-modal" role="tabpanel">
                        <div class="my-3">
                            <label for="input-email" class="form-label fs-5 text-secondary">E-mail</label>
                            <input type="email" id="input-email" class="form-control">
                        </div>
                        <div class="my-3">
                            <label for="input-password"
                                   class="form-label fs-5 text-secondary">{{ currentLangLabels.password }}</label>
                            <input type="password" id="input-password" class="form-control">
                        </div>
                    </div>

                    <!-- Register tab -->
                    <div class="tab-pane fade" id="register-modal" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="input-firstname" class="form-label fs-5 text-secondary">{{ currentLangLabels.name_label }}*</label>
                                <input type="text" id="input-firstname" class="form-control">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="input-lastname" class="form-label fs-5 text-secondary">{{ currentLangLabels.surname_label }}*</label>
                                <input type="text" id="input-lastname" class="form-control">
                            </div>
                            <div class="col-md-6 my-3">
                                <label for="input-register-email" class="form-label fs-5 text-secondary">E-mail*</label>
                                <input type="email" id="input-register-email" class="form-control">
                            </div>
                            <div class="col-md-6 my-3">
                                <label for="input-register-phone"
                                       class="form-label fs-5 text-secondary">{{ currentLangLabels.telephone_label }}*</label>
                                <input type="tel" id="input-register-phone" class="form-control">
                            </div>
                            <div class="my-3">
                                <label for="input-register-password"
                                       class="form-label fs-5 text-secondary">{{ currentLangLabels.password}}*</label>
                                <input type="password" id="input-register-password" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="btn-modal-login" type="button" class="btn btn-primary rounded-3 fs-5 text-white">
                    {{ currentLangLabels.login }}
                </button>
                <button id="btn-modal-close" type="button" class="btn btn-outline-secondary rounded-3  fs-5" data-bs-dismiss="modal">
                    {{ currentLangLabels.close }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Checkout Modal (Replicated from payment-login-modal) -->
<div class="modal fade" id="checkoutModal" tabindex="-1" aria-labelledby="checkoutModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">

            <div class="modal-header border-0">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
            </div>

            <div class="modal-body bg-light">
                <div class="container">
                    <div class="text-center mb-4">
                        <h5 class="fw-bold">{{currentLangLabels.last_step}}</h5>
                        <p class="text-muted">{{currentLangLabels.purchase_end}}</p>
                    </div>

                    <div class="card p-4">
                        <ul class="nav nav-tabs mb-4" id="clienteTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="login-tab" data-bs-toggle="tab"
                                        data-bs-target="#login" type="button" role="tab">
                                    {{ currentLangLabels.login_client }}
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="register-tab" data-bs-toggle="tab"
                                        data-bs-target="#register" type="button" role="tab">
                                    {{ currentLangLabels.login_newclient }}
                                </button>
                            </li>
                        </ul>

                        <!-- Tab content -->
                        <div class="tab-content" id="clienteTabsContent">
                            <!-- Login -->
                            <div class="tab-pane fade show active" id="login" role="tabpanel">
                                <form id="form-login">
                                    <div class="mb-3">
                                        <label for="email-login" class="form-label">E-mail*</label>
                                        <input type="email" id="email-login" class="form-control">
                                    </div>
                                    <div class="mb-3">
                                        <label for="password-login" class="form-label">{{currentLangLabels.password}}*</label>
                                        <input type="password" id="password-login" class="form-control">
                                    </div>
                                </form>
                            </div>

                            <!-- Registro -->
                            <div class="tab-pane fade" id="register" role="tabpanel">
                                <form id="form-register">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="firstname" class="form-label">{{ currentLangLabels.name_label }}*</label>
                                            <input type="text" id="firstname" class="form-control">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="lastname" class="form-label">{{ currentLangLabels.surname_label }}*</label>
                                            <input type="text" id="lastname" class="form-control">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="email-register" class="form-label">E-mail*</label>
                                            <input type="email" id="email-register" class="form-control">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="phone-register" class="form-label">{{ currentLangLabels.telephone_label }}*</label>
                                            <input type="tel" id="phone-register" class="form-control">
                                        </div>

                                        <div class="mb-3">
                                            <label for="password-register" class="form-label">{{ currentLangLabels.password }}*</label>
                                            <input type="password" id="password-register" class="form-control">
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Checkboxes comunes -->
                        <div class="mt-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="checkbox-privacy">
                                <label class="form-check-label" for="checkbox-privacy">
                                    {{currentLangLabels.accept}} <a href="#">{{currentLangLabels.privacy_policy}}</a>*
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="checkbox-communication">
                                <label class="form-check-label" for="checkbox-communication">
                                    {{currentLangLabels.commercial_label}}
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="checkbox-terms">
                                <label class="form-check-label" for="checkbox-terms">
                                    {{currentLangLabels.conditions_label}}*
                                </label>
                            </div>
                        </div>

                        <!-- Total y botón -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div class="total fw-bold text-success">{{currentLangLabels.total_label}}<span class="text-muted fs-6">({{currentLangLabels.taxes_included}})</span>
                            </div>
                            <div id="final-total-modal" class="fs-4 fw-bold text-success">0€</div>
                        </div>
                        <hr class="text-success">
                        <button onclick="pay()" type="submit" id="payment-btn" class="btn btn-success w-100 mt-3"
                                style="max-width: 250px">{{currentLangLabels.pay_now}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let isValid = false;
    let isFromEventDetails = false;

    $(document).ready(function () {
        // Detect if modal is opened from event details page or menu
        $('#checkoutModal').on('show.bs.modal', function (e) {
            isFromEventDetails = true;
            // Show payment button only when opened from event details
            $('#payment-btn').show();
        });

        $('#loginModal').on('show.bs.modal', function (e) {
            isFromEventDetails = false;
            // Hide payment button when opened from menu
            $('#payment-btn').hide();
        });

        // Check password on button click
        $('#btn-modal-login').on('click', function() {
            const activeTab = $('.tab-pane.active', '#loginModalTabsContent').attr('id');
            let password = '';
            
            if (activeTab === 'login-modal') {
                password = $("#input-password").val().trim();
            } else if (activeTab === 'register-modal') {
                password = $("#input-register-password").val().trim();
            }
            
            if (!password) {
                Swal.fire({
                    customClass: {
                        confirmButton: "btn btn-primary col-4 mx-1",
                        cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
                        title: 'h5',
                        actions: 'w-100 justify-content-center mt-4',
                    },
                    buttonsStyling: false,
                    icon: 'error',
                    title: 'Error!',
                    text: CURRENT_LANG_LABELS.wrong,
                });
                return false;
            }
        });

        // Update button text based on active tab
        $('#loginModalTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
            const targetTab = $(e.target).attr('data-bs-target');

            if (targetTab === '#login-modal') {
                btnModalLogin.text(defaultLoginText || '{{ currentLangLabels.login }}');
            } else if (targetTab === '#register-modal') {
                btnModalLogin.text(CURRENT_LANG_LABELS.register );
            }

            validateForm();
        });

        function validateForm() {
            const parentModal = $('.modal.show');
            const activeTabId = $('.tab-pane.active', parentModal).attr('id');
            let valid = false;

            if (activeTabId === 'login' || activeTabId === 'login-modal') {
                const emailInput = parentModal.find('input[type="email"]:visible').first();
                const passwordInput = parentModal.find('input[type="password"]:visible').first();
                const email = emailInput.length ? emailInput.val().trim() : '';
                const password = passwordInput.length ? passwordInput.val().trim() : '';
                valid = email.length > 0 && password.length > 0;
            }

            if (activeTabId === 'register' || activeTabId === 'register-modal') {
                const firstname = parentModal.find('input[id*="firstname"]:visible').val().trim();
                const lastname = parentModal.find('input[id*="lastname"]:visible').val().trim();
                const email = parentModal.find('input[type="email"]:visible').val().trim();
                const password = parentModal.find('input[type="password"]:visible').val().trim();
                const phone = parentModal.find('input[id*="phone"]:visible').val().trim();
                valid = firstname.length > 0 && lastname.length > 0 && email.length > 0 && password.length > 0;
            }

            // Only validate checkboxes for checkout modal
            if (parentModal.attr('id') === 'checkoutModal') {
                const checkPrivacy = $('#checkbox-privacy').is(':checked');
                const checkTerms = $('#checkbox-terms').is(':checked');
                valid = valid && checkPrivacy && checkTerms;
            }

            isValid = valid;

            // Only disable checkout button, never disable login button
            if (parentModal.attr('id') === 'checkoutModal') {
                $('#payment-btn').prop('disabled', !valid);
            }
        }

        // Para actualizar cuando el usuario escriba
        $('#checkoutModal input, #checkoutModal .form-check-input').on('input change', validateForm);
        $('#loginModal input').on('input change', validateForm);

        // También al cambiar de tab
        $('button[data-bs-toggle="tab"]').on('shown.bs.tab', function () {
            validateForm(); // Fuerza reevaluación global

            // También activa el evento keyup manual si es necesario
            const targetTab = $(this).attr('data-bs-target');
            if (targetTab === '#login-modal') {
                $('#input-email, #input-password').trigger('keyup');
            } else if (targetTab === '#register-modal') {
                $('#input-firstname, #input-lastname, #input-register-email, #input-register-password, #input-register-phone').trigger('keyup');
            }
        });



        // $('#email-login, #password-login, #firstname, #lastname, #email-register, #password-register, .form-check-input').on('input change', validateForm);
        // $('button[data-bs-toggle="tab"]').on('shown.bs.tab', validateForm);

        // Clear inputs when modal closes
        $('#loginModal, #checkoutModal').on('hidden.bs.modal', function () {
            $(this).find('input').val('');
            $(this).find('input[type="checkbox"]').prop('checked', false);

            // Only reset payment button, never disable login button
            if ($(this).attr('id') === 'checkoutModal') {
                $('#payment-btn').prop('disabled', true);
            }

            // Reset to login tab
            $(this).find('.nav-tabs .nav-link:first').tab('show');

            // Actualizar validación
            // validateForm();
        });
    });



    async function pay() {
        if (!isValid) {
            console.log('Not valid')
            Swal.fire({
                title: 'Error!',
                text: {{ currentLangLabels.data_error|json_encode|raw }},
                icon: 'success',
                customClass: {
                    confirmButton: "btn btn-primary col-4 mx-1",
                    cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
                    title: 'h5',
                    actions: 'w-100 justify-content-center mt-4',
                },
                buttonsStyling: false,
            });
            return
        }

        const parentModal = $('.modal.show'); // Currently open modal
        const activeTab = $('.tab-pane.active', parentModal).attr('id');
        let loginSuccess = false
        if (activeTab === 'login-modal' || activeTab === 'login') {
            const email = $('#email-login').val().trim();
            const password = $('#password-login').val().trim();

            let timerInterval;
            Swal.fire({
                customClass: {
                    confirmButton: "btn btn-primary col-4 mx-1",
                    cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
                    title: 'h5',
                    actions: 'w-100 justify-content-center mt-4',
                },
                buttonsStyling: false,
                title: {{ currentLangLabels.login_in|json_encode|raw }},
                text: {{ currentLangLabels.wait_moment|json_encode|raw }},
                timer: 5000,
                timerProgressBar: true,
                allowOutsideClick: false,
                allowEscapeKey: false,
                didOpen: () => {
                    Swal.showLoading();
                },
                willClose: () => {
                    clearInterval(timerInterval);
                }
            }).then((result) => {
                if (result.dismiss === Swal.DismissReason.timer && isFromEventDetails) {
                    paymentRequest()
                } else if (result.dismiss === Swal.DismissReason.timer && !isFromEventDetails) {
                    // Just close the modal if login is successful and not from event details
                    $('#loginModal').modal('hide');
                    $('#checkoutModal').modal('hide');
                }
            });
            loginSuccess = await loginRequest({email, password})
        } else {
            const firstname = $('#firstname').val().trim();
            const lastname = $('#lastname').val().trim();
            const email = $('#email-register').val().trim();
            const password = $('#password-register').val().trim();
            const phone = $('#phone-register').val().trim();

            let timerInterval;
            Swal.fire({
                customClass: {
                    confirmButton: "btn btn-primary col-4 mx-1",
                    cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
                    title: 'h5',
                    actions: 'w-100 justify-content-center mt-4',
                },
                buttonsStyling: false,
                title:  {{ currentLangLabels.login|json_encode|raw }},
                text: isFromEventDetails ?  {{ currentLangLabels.payment|json_encode|raw }}:  {{ currentLangLabels.account|json_encode|raw }},
                timer: 5000,
                timerProgressBar: true,
                didOpen: () => {
                    Swal.showLoading();
                },
                willClose: () => {
                    clearInterval(timerInterval);
                }
            }).then((result) => {
                if (result.dismiss === Swal.DismissReason.timer && isFromEventDetails) {
                    paymentRequest()
                } else if (result.dismiss === Swal.DismissReason.timer && !isFromEventDetails) {
                    // Just close the modal if registration is successful and not from event details
                    $('#loginModal').modal('hide');
                    $('#checkoutModal').modal('hide');
                }
            });
            loginSuccess = await registerRequest(firstname, lastname, email, password, phone)
            loginSuccess = await loginRequest({email, password})
        }

        if (!loginSuccess) {
            return
        }
    }

    function paymentRequest() {
        $.ajax({
            url: `{{ env.API_URL }}/services/book`,
            type: 'POST',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);
            },
            data: globalState,
            success: function (response) {
                const success_url = `${window.location.origin}/{{ currentLang }}/purchase/${response.id}/success`;
                const cancel_url = `${window.location.origin}/{{ currentLang }}/purchase/payment-failed`;
                const service_bookings_id = response.id;

                let url = `/redsys/openRedsysCheckout.php`;

                url += `?service_bookings_id=${encodeURIComponent(service_bookings_id)}`;
                url += `&success_url=${encodeURIComponent(success_url)}`;
                url += `&cancel_url=${encodeURIComponent(cancel_url)}`;

                window.location.href = url;
            },
            error: function (xhr, status, error) {
                Swal.fire({
                    
                    title: 'Error!',
                    text: {{ currentLangLabels.payment_error|json_encode|raw }},
                    icon: 'success',
                    customClass: {
                    confirmButton: "btn btn-primary col-4 mx-1",
                    cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
                    title: 'h5',
                    actions: 'w-100 justify-content-center mt-4',
                },
                buttonsStyling: false,
                });
            }
        })
    }

    function registerRequest(firstname, lastname, email, password, phone) {
        {
            return $.ajax({
                url: `{{ env.API_URL }}/customers/code`,
                type: 'POST',
                data: {
                    firstname,
                    lastname,
                    email,
                    password,
                    phone
                },
                success: function (response) {
                    // Success handling
                },
                error: function (xhr, status, error) {
                    console.log(error, "error")
                    Swal.fire({
                        customClass: {
                            confirmButton: "btn btn-primary col-4 mx-1",
                            cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
                            title: 'h5',
                            actions: 'w-100 justify-content-center mt-4',
                        },
                        buttonsStyling: false,
                        title: 'Error!',
                        text: "{{ currentLangLabels.email_inuse }}",
                        icon: 'error'
                    });
                }
            });
        }
    }
</script>
