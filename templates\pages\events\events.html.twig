{% extends 'base.html.twig' %}

{% block main %}
    <div class="container my-5">
        <h1 class="text-center my-5">{{currentLangLabels.events_header}}</h1>
        <!-- Filtro Select2 -->

        <div class="row align-items-center mb-4 g-3">
            <!-- Category Filter -->
            <div class="col-xl-3 d-flex justify-content-center">
                <select id="categories-filter" class="form-select w-100">
                    <option value="">{{ currentLangLabels.select_category }}</option>
                    {% for category in categories %}
                        {% set categoryContent = getContentFilteredByLang(category.content, 'language_code', currentLang, defaultLang) %}
                        <option value="{{ categoryContent.service_category_id }}">{{ categoryContent.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Order Filter -->
            <div class="col-xl-3 d-flex justify-content-center">
                <select id="order-filter" class="form-select w-100">
                    <option value="">{{ currentLangLabels.select_order }}</option>
                    <option value="">Otra opción</option>
                    <option value="1">{{ currentLangLabels.date_label }}</option>
                    <option value="2">{{ currentLangLabels.price_label }}</option>
                </select>
            </div>

            <!-- Age Group Checkboxes -->
            <div class="col-xl-3 d-flex justify-content-center">
                <div class="d-flex">
                    <div class="form-check me-2">
                        <input class="form-check-input" type="checkbox" id="filter-child" value="niño">
                        <label class="form-check-label" for="filter-child">{{currentLangLabels.child_label}}</label>
                    </div>
                    <div class="form-check me-2">
                        <input class="form-check-input" type="checkbox" id="filter-adult" value="adulto">
                        <label class="form-check-label" for="filter-adult">{{currentLangLabels.adult_label}}</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="filter-senior" value="jubilado">
                        <label class="form-check-label" for="filter-senior">{{currentLangLabels.senior_label}}</label>
                    </div>
                </div>
            </div>

            <!-- Price Range Inputs -->
            <div class="col-xl-2 d-flex justify-content-center">
                <div class="d-flex align-items-center">
                    <label class="me-2 fw-bold">{{currentLangLabels.price_label}}</label>
                    <input type="number" class="form-control me-2 p-2" id="price-min" placeholder="Min">
                    <input type="number" class="form-control p-2" id="price-max" placeholder="Max">
                </div>
            </div>

            <!-- Filter Button -->
            <div class="col-xl-1 d-flex justify-content-center">
                <button class="btn btn-primary p-2" id="apply-filters" onclick="applyFilter()">{{currentLangLabels.filter_label}}</button>
            </div>
        </div>


        <h1 class="text-center mb-4">{{ currentLangLabels.all_events }}</h1>

        <div class="row row-cols-1 row-cols-xl-3 gy-5 custom-gx">
            {% for service in services %}
                {% set serviceContent = getContentFilteredByLang(service.content, 'language_code', currentLang, defaultLang) %}
                {% set image = '/assets/img/placeholder.png' %}
                {% set imageClass = '' %}

                {% if service.image is not empty %}
                    {% set image = service.image %}
                    {% set imageClass = 'center-img' %}
                {% endif %}

                <div class="col service-card " data-id="{{ service.id }}"  data-date="{{ service.start_date|replace({'_': ' '})|date('Y-m-d\\TH:i:s\\Z', 'UTC') }}">
                    {% set image = image|default('/assets/img/placeholder.png') %}
                    {% set imageClass = imageClass|default('') %}
                    <div class="card h-100 border-0 rounded-1 shadow ">
                        <img src="{{ image }}" 
                            class="card-img-top fixed-img {{ imageClass }}" 
                            alt="{{ serviceContent.service }}">
                        
                        <div class="card-body ">
                            <h5 class="card-title">{{ serviceContent.service }}</h5>
                            <p class="card-text card-custom1 " style="display: none; color:red;">
                                {{ currentLangLabels.last_tickets }}
                            </p>
                            <p class="card-text card-custom2 " style="display: none; color:red;">
                                {{ currentLangLabels.no_spots }}
                            </p>
                            <p class="card-text">{{ serviceContent.short_description }}</p>
                        </div>
                        
                        <div class="card-footer bg-white border-top-0 text-center pb-3">
                            <a href="/{{ currentLang }}/events/{{ service.id }}" class="btn btn-primary w-100">
                                {{ currentLangLabels.more }}
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
          
        </div>
    </div>
     <!--select at the head of the container-->
    <script src="/assets/js/jquery/dist/jquery.min.js"></script>
    <link href="/assets/js/select2/dist/css/select2.min.css" rel="stylesheet" />
    <script src="/assets/js/select2/dist/js/select2.min.js"></script>
    <script>
        let originalCardMap = {};
        let cardsContainer = null; // Variable para guardar referencia al contenedor
        $(document).ready(function() {
            const categoryActivities = JSON.parse({{ var_export(calendarEvents|json_encode)|raw }});
            const categories = JSON.parse({{var_export(categories|json_encode)|raw }});
            const services = JSON.parse({{var_export(services|json_encode)|raw }});
            const serviceBookings = JSON.parse({{var_export(serviceBookings|json_encode)|raw }});    
            const customerPrices = JSON.parse({{var_export(customerPrices|json_encode)|raw }});
            const env = JSON.parse({{var_export(env|json_encode)|raw }});

            
            
            //fecha más cercana del evento de hoy para cada actividad
            let minDate = [];
            let idMinDate = [];
            let iteratorMinDate = -1;
            let minDateDiff = null;
            //cantidad de reservas para la actividad más temprana
            let reservesQuantity = [];
        
            // calcular la fecha más cercana del evento de hoy para cada actividad
           
            for (const activity of categoryActivities) {
                const today = moment().startOf('day');
                const activityEndDate = activity.end_date ? moment(espTimeZoneFormatter(activity.end_date, DATE_FORMAT)).startOf('day') : null;
                let startDate = moment(activity.start_date).utc(true).startOf('day'); // Opción 1   
                let startHour = espTimeZoneFormatter(activity.start_hour, TIME_FORMAT_NO_SECONDS);
                let endHour = espTimeZoneFormatter(activity.end_hour, TIME_FORMAT_NO_SECONDS);             
                const maxReserveDate = today.clone().add(activity.max_reserve_days, 'days');
                const maxDisplayDate = activityEndDate || maxReserveDate.clone().add(1, 'year');
                if(idMinDate[iteratorMinDate] != activity.id){
                    iteratorMinDate++;
                    idMinDate[iteratorMinDate] = activity.id;
                    
                    minDateDiff=null;
                }
                  
                 do {
                    for (const day of activity.periodicity_days) {
                        const eventDate = startDate.clone().add(day, 'days').startOf('day');
                        
                        // Verificar si el evento está después del end_date (si existe)
                        if (activityEndDate && eventDate.isAfter(activityEndDate)) {
                            continue;
                        }

                        // Verificar si el evento es anterior al inicio de la actividad
                        if (eventDate.isBefore(startDate)) {
                            continue;
                        }

                        const dateStr = eventDate.format(DATE_FORMAT);
                        const dateTimeStr = `${dateStr} ${startHour}`;
                        const isPast = moment(dateTimeStr).isBefore(moment());
                        const isReservable = !isPast && eventDate.isSameOrBefore(maxReserveDate);
                        
                        if (isReservable) {
                            const referenceDate = moment(dateTimeStr);
                            const currentDate = moment();
                            const substract = Math.abs(referenceDate.diff(currentDate));
                           
                            if(minDateDiff !=null){   
                                
                                if(substract < minDateDiff){
                                    
                                    
                                    minDateDiff = substract;
                                    
                                    minDate[iteratorMinDate] = dateTimeStr.replace(' ', '_').replace(/:(\d{2})$/, ':$1:00');;
                                }
                            }else{
                                minDate[iteratorMinDate] = dateTimeStr.replace(' ', '_').replace(/:(\d{2})$/, ':$1:00');;
                                minDateDiff=substract;
                            }
                           
                            
                        }   
                    }
                    
                    startDate = startDate.add(activity.periodicity, 'days');
                    
                } while (startDate.isSameOrBefore(maxDisplayDate));
                //si seguimos con la misma actividad, no incrementamos el iterador
               
            }
            
            //Calcular la cantidad de reservas para cada actividad
            for (let i = 0; i < idMinDate.length; i++) {
                reservesQuantity[i] = 0;
                
                for (const booking of serviceBookings) {
                    
                    if(booking.id == idMinDate[i] && booking.booking_time == minDate[i]){
                        
                        reservesQuantity[i] += booking.quantity;
                    }  
                }
                
            }

            //comprobar si se me están agotando las plazas para un evento, En caso de que se estén agotando, mostrar un mensaje
            
            //let iteratorReserves = 0;
            // Iterar sobre cada tarjeta de servicio
            document.querySelectorAll('.service-card').forEach((card, index) => {
                // Encontrar el mensaje condicional DENTRO de esta tarjeta específica
                const message_last_spots = card.querySelector('.card-custom1');
                const message_no_spots = card.querySelector('.card-custom2');
                
                // Verificar la condición para esta tarjeta específica
                if(reservesQuantity[index] ==  services[index].capacity){
                    message_no_spots.style.display = 'block';
                }else if(reservesQuantity[index] > (env.WARNING_CAPACITY_LIMIT_PERCENTAGE * services[index].capacity) / 100) {
                    message_last_spots.style.display = 'block';
                }
            });
            
            const currentLang = "{{ currentLang }}"; // 'en' o 'es'
            // Extraer los nombres filtrando por el idioma actual
            let categoryNames = [];
            let categoryId = [];

            for (let i = 0; i < categories.length; i++) {
                const contentArray = categories[i].content;

                if (Array.isArray(contentArray)) {
                    const names = contentArray
                        .filter(entry => entry.language_code === currentLang)
                        .map(entry => entry.name);

                    categoryNames = categoryNames.concat(names);
                }

                // Construir subarray de IDs desde services
                const services = categories[i].services;
                if (Array.isArray(services)) {
                    const serviceIds = services.map(service => service.id);
                    categoryId.push(serviceIds); // Agrega array de IDs de este i
                }
            }
            $('#categories-filter').select2({
                placeholder: "{{ currentLangLabels.category_label }}",
                allowClear: true
            });
            $('#order-filter').select2({
                placeholder: "{{ currentLangLabels.order_label }}",
                allowClear: true
            });

            $('.service-card').each(function () {
                const id = $(this).data('id');
                originalCardMap[id] = $(this).clone(true, true);
            });

            // Guardar referencia al contenedor de las tarjetas
            cardsContainer = $('.service-card').parent();

        });
    </script>
    <script>
        function applyFilter() {
            const filters = {
                childCheck: document.getElementById('filter-child').checked,
                adultCheck: document.getElementById('filter-adult').checked,
                seniorCheck: document.getElementById('filter-senior').checked,
                minPrice: document.getElementById('price-min').value || '',
                maxPrice: document.getElementById('price-max').value || '',
                categoriesId: $('#categories-filter').val(),
                orderValue: $('#order-filter').val()
            };

            if (filters.minPrice === '0'){
                filters.minPrice = '';
            }
            if (filters.maxPrice === '0'){
                filters.maxPrice = '';
            }

            const queryParams = new URLSearchParams(filters).toString();

            return new Promise((resolve, reject) => {
                $.ajax({
                    url: `${API_URL}/services/filtered-services?${queryParams}`,
                    type: 'GET',
                    success: function(response) {
                        const filteredItems = response?.data ?? [];

                        // Usar la referencia guardada del contenedor
                        cardsContainer.empty();

                        // Display in API order
                        filteredItems.forEach(item => {
                            const card = originalCardMap[item.id];
                            if (card) {
                                cardsContainer.append(card.clone(true, true).show());
                            }
                        });

                        resolve();
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching filtered services:', error);
                        reject(error);
                    }
                });
            });
        }
    </script>
    
{% endblock %}