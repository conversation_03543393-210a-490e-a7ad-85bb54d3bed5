document.querySelector("#discount-section").classList.remove("d-none");
const el = document.querySelector("#discount-section");
el.innerHTML = translations.discount_available;

// Find the discount_customer_type table
const discountTable = discountData.data.find(table => table.discount_table_name === "discount_customer_type");

if (discountTable && discountTable.items) {
    Object.entries(config.tickets).forEach(([type]) => {
        discountTable.items.forEach(row => {
            const discountDiv = document.createElement("div");
            discountDiv.classList.add("discount-type");

            let spanType = "";
            if (row.customer_type_id == 1 && type === 'adult') {
                spanType = translations.adulto;
            } else if (row.customer_type_id == 2 && type === 'child') {
                spanType = translations.nino;
            } else if (row.customer_type_id == 3 && type === 'senior') {
                spanType = translations.jubilado;
            }
            if(spanType !== "") {
                discountDiv.innerHTML = `${spanType} ${translations.de} ${row.discount}${row.discount_type} ${translations.del_ticket} ${row.from} ${translations.al_ticket} ${row.to}.`;
                discountDiv.className = "small fw-light text-muted";

                document.querySelector("#discount-section").appendChild(discountDiv);
            }
        });
    });
}

function calculateDiscount() {
    let total_discount = 0;

    Object.entries(config.tickets).forEach(([type,data]) => {
        const count = parseInt(document.getElementById(`ticket-${type}`).textContent) || 0;
        const price = data.price;
        let ticketId = 0;
        let totalTypeDiscount = 0;

        switch (type) {
            case 'adult':
            ticketId = 1;
            break;
            case 'child':
            ticketId = 2;
            break;
            case 'senior':
            ticketId = 3;
            break;
            default:
            ticketId = 0;
            break;
        }
        // Find the discount_customer_type table
        const discountTable = discountData.data.find(table => table.discount_table_name === "discount_customer_type");
        console.log(discountTable);

        if (count > 0 && discountTable && discountTable.items) {
            discountTable.items.forEach(row => {
                if (row.customer_type_id == ticketId) {
                    const applicableCount = Math.max(0, Math.min(count, row.to) - row.from );
                    const discount = parseFloat(row.discount);
                    const discount_type = row.discount_type;
                    if (applicableCount > 0) {
                        switch (discount_type) {
                            case '%':
                                const percentDiscount = (price * discount) / 100;
                                totalTypeDiscount += percentDiscount * applicableCount;
                                break;
                            case '€':
                            default:
                                totalTypeDiscount = discount * applicableCount;
                                break;
                        }
                        globalState.discount= row.discount_id;
                    }
                }
            });
        }
        total_discount += totalTypeDiscount;
        updateDiscountSpan(type, totalTypeDiscount);
    });
    return total_discount;
}