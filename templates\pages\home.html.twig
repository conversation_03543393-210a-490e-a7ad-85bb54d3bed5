{% extends 'base.html.twig' %}
{% block main %}
    <!-- <PERSON><PERSON><PERSON> el <PERSON>S de Splide -->

    <!-- Estilos personalizados para el slider -->
    <style>
        /* Estilos base del slider */

        .splide__track {
            height: auto !important;
            min-height: 560px;
            padding-bottom: 1rem;
            overflow: visible;
        }

        @media (max-width: 768px) {
            .splide__track {
                min-height: 420px;
            }
        }

        @media (max-width: 576px) {
            .splide__track {
                min-height: unset;
                padding-bottom: 4rem;
            }
        }


        /*.splide__list {*/
        /*    height: auto !important;*/
        /*    overflow: visible !important;*/
        /*    align-items: stretch;*/
        /*}*/

        /*.splide__slide {*/
        /*    height: auto !important;*/
        /*    display: flex;*/
        /*    flex-direction: column;*/
        /*    padding: 0 1rem; !*separar cards entre si*!*/
        /*}*/

        /*.splide__slide.is-active {*/
        /*    opacity: 1;*/
        /*}*/

        /*.splide__slide.is-active{*/
        /*    transform: scale(1);*/
        /*    opacity: 1;*/
        /*    z-index: 2;*/
        /*}*/

        /*.splide__slide {*/
        /*    transition: transform 0.3s ease, opacity 0.3s ease;*/
        /*    transform: scale(1);*/
        /*    opacity: 1;*/
        /*}*/
        @media (min-width: 576px) {
            .splide__slide.is-active .slide-box {
                padding: 0;
                /*transition: transform 0.3s ease, opacity 0.3s ease;*/
                /*transform: scale(1);*/
                z-index: 2;
            }

            .splide__slide .slide-box {
                padding: 4rem;
                transition: padding 0.5s ease;
            }

        }

        .splide__slide .slide-box {

            height: 100%;

        }

        .card {
            height: 100%;
            border-radius: 0.5rem !important; /*borde redondeado*/
        }

        .splide__slide .card-img-container {
            height: 330px;
            overflow: visible;
        }


        .splide__slide.is-active .card-img-container {
            height: 450px;
            overflow: visible;
        }

        .card-img-container img {
            width: 100%;
            height: 100%;
            border-radius: 0.5rem; /*borde redondeado*/
            object-fit: cover;
        }


        @media (max-width: 768px) {
            .splide__slide .card-img-container {
                height: 200px;
            }

            .card-title.display-4 {
                font-size: 2rem !important;
            }

            .lead {
                font-size: 1rem !important;
            }
        }

        @media (max-width: 576px) {
            .splide__slide .card-img-container {
                height: 180px;
            }

            .card-body {
                padding: 1rem !important;
            }
        }

        /* Estilos para los botones de navegación */
        /* Default pagination positioning */
        .splide__pagination {
            bottom: -2.5rem !important;
        }

        /* Adjust pagination position on smaller screens */
        @media (max-width: 768px) {
            .splide__pagination {
                bottom: -1rem !important;
            }
        }

        @media (max-width: 576px) {
            .splide__pagination {
                bottom: 0.5rem !important;
            }
        }


        .splide__pagination__page {
            width: 8px !important;
            height: 8px !important;
            margin: 0 6px !important;
            background: #d4d4d4 !important;
            border-radius: 4px !important;
            transition: all 0.3s ease-in-out !important;
            opacity: 1 !important;
            border: none !important;
            position: relative !important;
        }

        .splide__pagination__page:hover {
            background: #b0b0b0 !important;
            transform: scale(1.2) !important;
        }

        .splide__pagination__page.is-active {
            transform: scale(1.2) !important;
            background: #004d7a !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
        }


        /* Estilos responsivos solo para el layout del calendario */
        @media (max-width: 768px) {
            #calendar-home .fc-header-toolbar {
                flex-direction: column !important;
                align-items: stretch !important;
                gap: 0.75rem !important;
            }

            #calendar-home .fc-header-toolbar .fc-toolbar-chunk {
                justify-content: center !important;
            }

            #calendar-home .fc-header-toolbar .fc-toolbar-chunk:first-child {
                order: 1;
            }

            #calendar-home .fc-header-toolbar .fc-toolbar-chunk:nth-child(2) {
                order: 2;
            }

            #calendar-home .fc-header-toolbar .fc-toolbar-chunk:last-child {
                order: 4;
            }

            /* El select2 va en la posición 3 */
            #calendar-home .fc-header-toolbar .select2-container {
                order: 3 !important;
                width: 100% !important;
                max-width: 100% !important;
            }
        }

        .swiper-button-next, .swiper-button-prev {
            color: #bec76f;
        }

        .swiper-container {
            width: 50%;
            /*height: 100%;*/
            height: 80%;
            margin-left: auto;
            margin-right: auto;
        }

        .swiper-slide {
            text-align: center;
            font-size: 18px;
            /*background: #fff;*/
            height: 100%;
            color: #000;
            /* 	transform: scale(0.8); */
            /* Center slide text vertically */
            display: -webkit-box;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-box-align: center;
            -ms-flex-align: center;
            -webkit-align-items: center;
            align-items: center;
            transition: 0.3s all;
            /*box-shadow: 0 0 20px #00000036;*/
            /*border-radius: 10px;*/
        }

        .swiper-slide:not(.swiper-slide-prev):not(.swiper-slide-active):not(.swiper-slide-next) {
            opacity: 0;
        }

        .swiper-slide-prev,
        .swiper-slide-next {
            opacity: 0.3;
        }

        .swiper-slide-active {
            transition: 0.3s all;
            opacity: 1;
            /*background-color: orange;*/
        }

        /*.swiper-pagination{*/
        /*    position: relative;*/
        /*    margin-top: 30px*/
        /*}*/

        .card-slide {
            min-height: calc(90vh - 270px);
            /*height: 15vh;*/
            max-height: 80vw;
        }

        .bt-container-slider {
            height: calc(100vh - 180px);
        }


    </style>


    <div>
        <h1 class="text-center mt-5">{{ currentLangLabels.home_header }}</h1>
        <div class="container bt-container-slider overflow-x-hidden">
            <div class="swiper-container my-5">
                <div class="swiper-wrapper">

                    {% for service in events %}

                        {% set serviceContent = getContentFilteredByLang(service.content, 'language_code', currentLang, defaultLang) %}

                        <div class="swiper-slide">
                            {% if loop.last %}

                                <div class="card card-slide shadow border-0 text-white"
                                     style="background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('{{ service.image }}') center/cover no-repeat;">
                                    <div
                                         class="card-body d-flex flex-column justify-content-center align-items-center"
                                    >
                                        <div class="my-auto">
                                            <div class=" first-card-text-title fs-1 mb-1 text-center"
                                                 style=" text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);">
                                                {{ serviceContent.service }}
                                            </div>
                                            <div class="first-card-text-description mb-4 fs-2 text-center"
                                                 style=" line-height: 1.2;margin-top: -0.75rem;text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);">
                                                {{ serviceContent.short_description }}
                                            </div>
                                        </div>
                                        <a href="/{{ currentLang }}/events"
                                           class="btn btn-primary rounded-3 fs-5 d-inline-block mx-auto mt-auto mb-2 w-80"
                                           style="width: 98%; min-width: 120px;">
                                            {{ currentLangLabels.show_more|default('Ver más') }}
                                        </a>
                                    </div>
                                </div>
                            {% else %}
                                <div class="card card-slide shadow border-0">
                                    <div class="card-img-container ">
                                        <img class="rounded-bottom-0"
                                             src="{{ service.image|default('/assets/img/placeholder.png') }}"
                                             alt="{{ serviceContent.service }}">
                                    </div>
                                    <div class="card-body d-flex flex-column justify-content-center">
                                        <h5 class="card-title ">{{ serviceContent.service }}</h5>
                                        <div class="text-secondary categories-accordion-description"
                                             style="overflow-y: auto;">
                                            {{ serviceContent.short_description }}
                                        </div>
                                    </div>
                                    <div class="card-footer bg-white border-top-0 text-center">
                                        {# <a href="{{ service.link }}"
                                           class="btn btn-primary rounded-2 fs-5 d-inline-block mx-auto mt-2 mb-4"
                                           style="width: 95%; min-width: 120px;">
                                            {{ currentLangLabels.show_more|default('Ver más') }}
                                        </a> #}
                                        <a href="/{{ currentLang }}/events/{{ service.id }}"
                                           class="btn btn-primary rounded-2 fs-5 d-inline-block mx-auto w-100 my-2">
                                            {{ currentLangLabels.show_more|default('Ver más') }}
                                        </a>
                                    </div>
                                </div>
                            {% endif %}

                        </div>
                    {% endfor %}

                </div>
                <!-- Add Pagination -->
                {#        <div class="swiper-pagination"></div> #}

            </div>
            <div class="swiper-button-prev"></div>
            <div class="swiper-button-next"></div>
        </div>


        <div class="container" style="margin-top: 100px; margin-bottom: 100px;">

            <div id='calendar-home'></div>
        </div>

    </div>

    <!-- Splide -->
    {#    <script src="/assets/js/jquery/dist/jquery.min.js"></script> #}

    <script>
        document.addEventListener('DOMContentLoaded', function () {



            let splideInstance;

            function mountSplide() {
                if (splideInstance) {
                    splideInstance.destroy(true);
                }

                splideInstance = new Splide('.splide', {
                    type: 'slide',
                    start: 1,
                    arrows: true,
                    pagination: true,
                    focus: 'center',
                    perPage: 3,
                    perMove: 1,
                    gap: '0rem',
                    rewind: true,
                    // padding: {
                    //     right: '5rem',
                    //     left: '5rem'
                    // },
                    height: 'auto',
                    breakpoints: {
                        1200: {
                            perPage: 2,
                            padding: {
                                right: '3rem',
                                left: '3rem'
                            }
                        },
                        992: {
                            perPage: 1.5,
                            padding: {
                                right: '2rem',
                                left: '2rem'
                            }
                        },
                        768: {
                            perPage: 1,
                            arrows: true,
                            pagination: false,
                            padding: {
                                right: '1rem',
                                left: '1rem'
                            }
                        },
                        576: {
                            perPage: 1,
                            gap: '0.5rem',
                            arrows: true,
                            pagination: false,
                            padding: 0
                        }
                    }
                });

                splideInstance.mount();
            }

            // Initial mount
            mountSplide();

            // Remount on resize to ensure pagination recalculates
            window.addEventListener('resize', function () {
                clearTimeout(window._splideResizeTimeout);
                window._splideResizeTimeout = setTimeout(() => {
                    mountSplide();
                }, 200);
            });
        });
    </script>

    <script>
        var swiper = new Swiper(".swiper-container", {
                slidesPerView: 1,
                loop: true,
                effect: "coverflow",
                grabCursor: true,
                centeredSlides: true,
                spaceBetween: -100,
                coverflowEffect: {
                    rotate: 0,
                    stretch: 0,
                    depth: 800,
                    modifier: 1,
                    slideShadows: false
                },
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true
                },
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev"
                },
            });
        $(document).ready(function () {



            const categoryActivities = JSON.parse({{ var_export(calendarEvents|json_encode)|raw }});
            const categories = JSON.parse({{ var_export(categories|json_encode)|raw }});
            const serviceCategories = JSON.parse({{ serviceCategories|json_encode|raw }});


            const currentLang = "{{ currentLang }}"; // 'en' o 'es'
            // Extraer los nombres filtrando por el idioma actual
            let categoryNames = [];
            let categoryId = [];


            for (let i = 0; i < categories.length; i++) {
                const contentArray = categories[i].content;

                if (Array.isArray(contentArray)) {
                    const names = contentArray
                        .filter(entry => entry.language_code === currentLang)
                        .map(entry => entry.name);

                    categoryNames = categoryNames.concat(names);
                }

                // Construir subarray de IDs desde services
                const services = categories[i].services;
                if (Array.isArray(services)) {
                    const serviceIds = services.map(service => service.id);
                    categoryId.push(serviceIds); // Agrega array de IDs de este i
                }
            }


            const calendarElement = document.getElementById('calendar-home');
            let calendarEvents = [];

            // Array de colores para los eventos
            const eventColors = ['#bec76f', '#abb363', '#989f58', '#858b4d', '#727742', '#5f6337', '#4c4f2c'];
            const eventColorMap = new Map(); // Mapa para guardar color por nombre de evento
            let colorIndex = 0;

            // Función para obtener el color para un evento
            function getEventColor(activityName) {
                if (!eventColorMap.has(activityName)) {
                    eventColorMap.set(activityName, eventColors[colorIndex]);
                    colorIndex = (colorIndex + 1) % eventColors.length; // Volver al inicio si se acabaron los colores
                }
                return eventColorMap.get(activityName);
            }

            for (const activity of categoryActivities) {
                const today = moment().startOf('day');
                const maxReserveDate = today.clone().add(activity.max_reserve_days, 'days');
                const activityEndDate = activity.end_date ? moment(espTimeZoneFormatter(activity.end_date, DATE_FORMAT)).startOf('day') : null;

                let startDate = moment(activity.start_date).utc(true).startOf('day'); // Opción 1
                //let startDate = moment(espTimeZoneFormatter(activity.start_date, DATE_FORMAT));
                let startHour = espTimeZoneFormatter(activity.start_hour, TIME_FORMAT_NO_SECONDS);
                let endHour = espTimeZoneFormatter(activity.end_hour, TIME_FORMAT_NO_SECONDS);
                const eventColor = getEventColor(activity.content.activity);

                // 1. Verificar si la actividad ya terminó (end_date existe y es pasado)
                if (activityEndDate && activityEndDate.isBefore(today)) {
                    continue; // Saltar completamente esta actividad
                }

                // 2. Procesar todos los eventos visibles (pasados y futuros)
                const maxDisplayDate = activityEndDate || maxReserveDate.clone().add(1, 'year');

                do {
                    for (const day of activity.periodicity_days) {
                        const eventDate = startDate.clone().add(day, 'days').startOf('day');

                        // Verificar si el evento está después del end_date (si existe)
                        if (activityEndDate && eventDate.isAfter(activityEndDate)) {
                            continue;
                        }

                        // Verificar si el evento es anterior al inicio de la actividad
                        if (eventDate.isBefore(startDate)) {
                            continue;
                        }

                        const dateStr = eventDate.format(DATE_FORMAT);
                        const dateTimeStr = `${dateStr} ${startHour}`;
                        const isPast = moment(dateTimeStr).isBefore(moment());
                        const isReservable = !isPast && eventDate.isSameOrBefore(maxReserveDate);

                        if (isReservable) {
                            // Evento disponible para reserva (color normal)
                            calendarEvents.push({
                                title: `${startHour} - ${endHour} ${activity.content.activity}`,
                                start: `${dateStr} ${activity.start_hour}`,
                                end: `${dateStr} ${activity.end_hour}`,
                                description: activity.content.short_description,
                                url: `/{{ currentLang }}/events/${activity.id}?start=${dateStr}T${activity.start_hour}&end=${dateStr}T${activity.end_hour}`,
                                textColor: '#ffffff',
                                backgroundColor: eventColor,
                                borderColor: eventColor,
                                display: 'block'
                            });
                        } else {
                            // Evento no reservable (gris) - incluye pasados y futuros fuera de max_reserve_days
                            calendarEvents.push({
                                title: `${startHour} - ${endHour} ${activity.content.activity}`,
                                start: `${dateStr} ${activity.start_hour}`,
                                end: `${dateStr} ${activity.end_hour}`,
                                description: activity.content.short_description,
                                textColor: '#ffffff',
                                backgroundColor: '#919494',
                                borderColor: '#919494',
                                display: 'block'
                            });
                        }
                    }
                    startDate = startDate.add(activity.periodicity, 'days');

                } while (startDate.isSameOrBefore(maxDisplayDate));
            }

            const firstDay = 1

            function initCalendar(calendarElement, calendarEvents) {
                const isMobile = $(window).width() < 768;
                const defaultView = isMobile ? 'listDay' : 'dayGridWeek';

                let calendar = new FullCalendar.Calendar(calendarElement, {
                    initialView: defaultView,
                    displayEventTime: false,
                    height: 500,
                    firstDay,
                    locale: "{{ currentLang }}",
                    headerToolbar: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'dayGridDay,dayGridWeek,dayGridMonth,listMonth'
                    },
                    buttonText: {
                        today: "{{ currentLangLabels.calendar_today }}",
                        day: '{{ currentLangLabels.calendar_day }}',
                        week: '{{ currentLangLabels.calendar_week }}',
                        month: '{{ currentLangLabels.calendar_month }}',
                        list: '{{ currentLangLabels.calendar_list }}'
                    },
                    views: {
                        dayGridDay: {buttonText: '{{ currentLangLabels.calendar_day }}'},
                        dayGridWeek: {buttonText: '{{ currentLangLabels.calendar_week }}'},
                        dayGridMonth: {buttonText: '{{ currentLangLabels.calendar_month }}'},
                        listMonth: {buttonText: '{{ currentLangLabels.calendar_today }}'}
                    },
                    events: calendarEvents
                });

                $(window).resize(function () {
                    const newView = $(window).width() < 768 ? 'listDay' : 'dayGridWeek';
                    calendar.changeView(newView);
                });

                calendar.render();
                // wait until de calendar renderization
                setTimeout(() => {
                    // create select
                    const $select = $(`
                        <select id="calendar-filter">
                            <option value="">Todas las actividades</option>
                            ${categoryNames.map(name => `<option value="${name}">${name}</option>`).join('')}
                        </select>
                    `);


                    // Insert the select beforee the title
                    const toolbar = $(calendarElement).find('.fc-toolbar.fc-header-toolbar');
                    const leftSection = toolbar.find('.fc-toolbar-chunk').first(); // chunk que contiene prev,next,today
                    const titleSection = toolbar.find('.fc-toolbar-chunk').eq(1); // chunk que contiene el título

                    // insert select after today button
                    leftSection.append($select);

                    // Initialize select
                    $('#calendar-filter').select2({
                        placeholder: CURRENT_LANG_LABELS.activities_filter,
                        allowClear: true
                    });
                    const $container = $('#calendar-filter').next('.select2-container');

                    function updateSelect2Classes() {
                        if (window.matchMedia('(max-width: 992px)').matches) {
                            // Mobile
                            $container.removeClass('ms-3');
                            $container.addClass('mt-3');
                        } else {
                            // Desktop
                            $container.removeClass('mt-3');
                            $container.addClass('ms-3');
                        }
                    }

                    // Initial call
                    updateSelect2Classes();

                    $(window).on('resize', updateSelect2Classes);

                    $container.css('margin-left', '0');

                    // Filter events when seclection
                    /*$('#calendar-filter').on('change', function () {
                        const selected = $(this).val();

                        if (!selected) {
                            calendar.removeAllEventSources();
                            calendar.addEventSource(calendarEvents);
                        } else {
                            const filteredEvents = calendarEvents.filter(event =>
                                event.title.includes(selected)
                            );
                            calendar.removeAllEventSources();
                            calendar.addEventSource(filteredEvents);
                        }
                    });*/

                    // Filter events when seclection
                    $('#calendar-filter').on('change', function () {
                        const selected = $(this).val();

                        if (!selected) {
                            calendar.removeAllEventSources();
                            calendar.addEventSource(calendarEvents);
                            return;
                        }

                        // Buscar la posición del nombre seleccionado
                        const position = categoryNames.indexOf(selected);
                        const selectedCategoryIds = categoryId[position]; // IDs de servicios para esa categoría

                        // Obtener nombres de actividad que coinciden con esos IDs
                        const selectedActivityNames = categoryActivities
                            .filter(activity => selectedCategoryIds.includes(activity.id))
                            .map(activity => activity.content.activity);

                        // Filtrar eventos del calendario por esos nombres de actividad
                        const filteredEvents = calendarEvents.filter(event =>
                            selectedActivityNames.some(name => event.title.includes(name))
                        );

                        calendar.removeAllEventSources();
                        calendar.addEventSource(filteredEvents);
                    });
                }, 10);

            }

            initCalendar(calendarElement, calendarEvents);

            //categoryActivities[0].id
            //categories[i].services[j].id

        });
    </script>

{% endblock %}
