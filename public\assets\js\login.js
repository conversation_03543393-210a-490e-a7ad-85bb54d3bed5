const btnLogin = $('#btn-login');
const btnModalLogin = $('#btn-modal-login');
const btnUser = $('#btn-user');
const btnLogout = $('#btn-logout');
const divUser = $('#div-user');



let isLoggedIn = false;
let isRefreshed = false;
let defaultLoginText = btnLogin.text();
let defaultBookingNowText = ''
let defaultBookingText = '';
let activeTab = '';


function initLoginModal() {
    btnLogin.prop("disabled", false);

    // Make sure login button starts enabled and stays enabled until POST
    $('#loginModal').on('shown.bs.modal', function () {
        btnModalLogin.prop("disabled", false);
    });
    
    // Clear validation state when modal is hidden
    $('#loginModal').on('hidden.bs.modal', function () {
        btnModalLogin.prop("disabled", false);
    });

    // Add password validation on button click
    btnModalLogin.on('click', function(e) {
        const activeTab = $('.tab-pane.active', '#loginModalTabsContent').attr('id');
        let password = '';
        
        if (activeTab === 'login-modal') {
            password = $("#input-password").val().trim();
        } else if (activeTab === 'register-modal') {
            password = $("#input-register-password").val().trim();
        }
        
        if (!password) {
            e.preventDefault();
            e.stopPropagation();
            Swal.fire({
                customClass: {
                    confirmButton: "btn btn-primary col-4 mx-1",
                    cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
                    title: 'h5',
                    actions: 'w-100 justify-content-center mt-4',
                },
                buttonsStyling: false,
                icon: 'error',
                title: 'Error!',
                text: CURRENT_LANG_LABELS.wrong,
            });
            return;
        }
    });

    btnLogout.on('click', () => {
        logout()
    })

    // if (accessToken.length > 0 && refreshToken.length > 0)
    //     login();

    // $("#input-email , #input-password").on("keyup", () => {
    //     const email = $("#input-email").val();
    //     const password = $("#input-password").val();
    //     /*   if ((email.includes('@') && email.includes('.')) && email.length >= 3 && password.length > 0) {
    //           btnLogin.prop("disabled", false);
    //       } */
    // });

    btnModalLogin.on("click", () => {
        activeTab = $('.tab-pane.active', '#loginModalTabsContent').attr('id');

        if (activeTab === 'login-modal') {
            // Handle login
            const email = $("#input-email").val();
            const password = $("#input-password").val();

            // Only check if fields are not empty, no email validation here
            if (email.trim().length > 0 && password.trim().length > 0) {
                const body = {
                    email,
                    password
                }

                defaultLoginHtml = CURRENT_LANG_LABELS.login
                btnModalLogin.prop("disabled", true);
                btnModalLogin.html('<i class="fa-solid fa-spinner fa-spin"></i>')
                loginRequest(body).then((res) => {
                    if (res) $('#loginModal').modal('hide')
                    btnModalLogin.html(defaultLoginHtml).prop("disabled", false);
                }).catch((e) => {
                    btnModalLogin.html('<i class="fa-solid fa-spinner fa-spin">' + btnLogin.text() + '</i>').text(defaultLoginHtml).prop("disabled", false);
                    Swal.fire({
                        customClass: {
                            confirmButton: "btn btn-primary col-4 mx-1",
                            cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
                            title: 'h5',
                            actions: 'w-100 justify-content-center mt-4',
                        },
                        buttonsStyling: false,
                        icon: 'error',
                        title: 'Error!',
                        text: CURRENT_LANG_LABELS.wrong,
                    });
                })
            }
        } else if (activeTab === 'register-modal') {
            // Handle registration
            const firstname = $("#input-firstname").val();
            const lastname = $("#input-lastname").val();
            const email = $("#input-register-email").val();
            const password = $("#input-register-password").val();
            const phone = $("#input-register-phone").val();
            /*  $(document).on('click', '#btn-modal-close', function () {
                 btnLogin.prop("disabled", false);
             }) */

            // Only check if required fields are not empty, no email validation here
            if (firstname.trim().length > 0 && lastname.trim().length > 0 && email.trim().length > 0 && password.trim().length > 0) {
                btnModalLogin.prop("disabled", true);

                btnModalLogin.html('<i class="fa-solid fa-spinner fa-spin"></i>')

                // Call register function
                $.ajax({
                    url: `${API_URL}/customers/code`,
                    type: 'POST',
                    data: {
                        firstname,
                        lastname,
                        email,
                        password,
                        phone
                    },
                    success: function (response) {
                        // After successful registration, log in the user
                        loginRequest({ email, password }).then((res) => {
                            if (res) $('#loginModal').modal('hide')
                            btnModalLogin.html('').text(defaultLoginText).prop("disabled", false);
                        }).catch((e) => {

                            btnModalLogin.html('').text(CURRENT_LANG_LABELS.register).prop("disabled", false);
                        });
                    },
                    error: function (xhr, status, error) {

                        btnModalLogin.html('').text(CURRENT_LANG_LABELS.register).prop("disabled", false);
                        Swal.fire({
                            customClass: {
                                confirmButton: "btn btn-primary col-4 mx-1",
                                cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
                                title: 'h5',
                                actions: 'w-100 justify-content-center mt-4',
                            },
                            buttonsStyling: false,
                            icon: 'error',
                            title: 'Error!',
                            text: CURRENT_LANG_LABELS.email_inuse,
                        });
                    }
                });
            }
        }
    })
}

function initLoginBooking(type, bookingObject) {
    $(document).on('click', '#btn-modal-book-now', function () {
        $(this).prop("disabled", true);
        $(this).html('<i class="fa-solid fa-spinner fa-spin"></i>')
        const email = $("#input-book-email").val();
        const password = $("#input-book-password").val();

        if (email.length > 0 && password.length > 0) {
            const body = {
                email,
                password
            }

            loginRequest(body).then((res) => {

                let isValidQty = false;

                if (type === 'services') {
                    const selectedQty = parseInt($('#input-qty-to-book').val())
                    const availableQty = parseInt($('#input-qty-to-book').attr('max'))
                    if (selectedQty <= availableQty) {
                        isValidQty = true
                    }
                } else {
                    if ($('#input-qty-to-book').val() <= availableQty) isValidQty = true;
                }

                if (isValidQty) {
                    // $('#booking-modal').modal('hide')
                    initPaymentClick(bookingObject)

                    // $('#btn-modal-book-now').html('').text(defaultBookingNowText).prop("disabled", false);

                    $('#booking-modal').modal('hide');
                    $('#payment-modal').modal('show');
                } else {
                    exeededSlotsError($('#input-qty-to-book').attr('max'))
                }

                $('#booking-login').removeClass('d-none')
                if (isLoggedIn) $('#booking-login').addClass('d-none')
                $('#btn-modal-book-now').html('').text(defaultBookingNowText).prop("disabled", false);
            }).catch((e) => {
                //todo:SWAL
                /* Swal.fire({
                     title: 'Error!',
                     text: 'Do you want to continue',
                     icon: 'error',
                     confirmButtonText: 'Cool'
                 })*/
            })
        }
    })
}

async function loginRequest(body) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: "/api/login.php",
            method: "POST",
            contentType: "application/json",
            dataType: "json",
            data: JSON.stringify(body)
        }).done((response) => {


            if (!response.success) {
                wrongUser();
                resolve(false);
                return;
            }

            if (Object.keys(response).length < 1) {
                wrongUser();
                resolve(false);
                return;
            }

            accessToken = response.data.access_token;
            refreshToken = response.data.refresh_token;
            localStorage.setItem("accessToken", accessToken);
            localStorage.setItem("refreshToken", refreshToken);
            login(accessToken);
            resolve(true);
        }).fail((xhr) => {
            console.log('Error:', xhr.responseText || xhr);
            wrongUser();
            reject(xhr);
        });

    })

}

function login(accessToken) {
    // let data = accessToken.split(".");
    const data = parseJwt(accessToken);
    // data = JSON.parse(atob(data[1]));
    btnUser.text(data.firstname);
    btnLogin.prop("hidden", true);
    divUser.prop("hidden", false);
    isLoggedIn = true;
}

function logout() {
    $.ajax({
        url: '/api/logout.php',
        type: 'DELETE',
        success: function (response) {
            console.log('Delete request successful');
            // Clear localStorage
            localStorage.removeItem("accessToken");
            localStorage.removeItem("refreshToken");
            // Handle the response here
            btnLogin.prop("hidden", false);
            divUser.prop("hidden", true);
            isLoggedIn = false;
            if (window.location.href.includes('/bookings')) {
                window.location.href = `/${CURRENT_LANG}/home`;
            }
        },
        error: function (xhr, status, error) {
            console.error('Error deleting resource:', error);
            // Handle the error here
        }
    });
}

function wrongUser() {
    logout();
    // $('#loginModal').modal('hide')
    btnModalLogin.html('')
    btnModalLogin.text(defaultLoginText)
    btnModalLogin.prop("disabled", false);
    Swal.fire({
        customClass: {
            confirmButton: "btn btn-primary col-4 mx-1",
            cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
            title: 'h5',
            actions: 'w-100 justify-content-center mt-4',
        },
        buttonsStyling: false,
        title: 'Error!',
        text: 'Wrong email or password',
        icon: 'error',
        showConfirmButton: false,
    })
}

function checkAndLogin(accessToken) {
    const tokenParts = accessToken.split('.');
    if (tokenParts.length !== 3) {
        console.error('Invalid token format');
        return;
    }

    try {
        const payload = JSON.parse(atob(tokenParts[1]));
        const nowUtcDate = moment.utc();
        const payloadUtcDate = moment.unix(payload.exp).utc();

        if (payload.exp && payloadUtcDate.isBefore(nowUtcDate) && !isRefreshed) {
            // if (payload.exp && payload.exp > now) {
            refreshAuthToken().then(success => {
                if (success && accessToken) {
                    checkAndLogin(accessToken); // retry with refreshed token
                } else {
                    console.warn("Unable to refresh token. User needs to log in again.");
                }
            });
        } else {
            login(accessToken);
        }
    } catch (e) {
        console.error("Failed to decode token:", e);
    }
}

function refreshAuthToken() {
    isRefreshed = true;
    return new Promise((resolve) => {
        refreshToken = localStorage.getItem("refreshToken");

        if (!refreshToken) {
            console.warn("No refresh token found in localStorage.");
            resolve(false);
            return;
        }

        $.post("/api/refreshToken.php", {
            token: refreshToken // debe estar disponible en tu JS
        }).done((data) => {
            if (data?.['success']) {
                accessToken = data['data']['access_token'];
                resolve(true);
            } else {
                resolve(false);
            }
        }).fail((e) => {
            console.log('Error refreshing token:', e);
            resolve(false);
        });
    });
}



