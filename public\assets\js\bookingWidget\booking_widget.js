function incrementTicket(type) {
  const input = document.getElementById(`ticket-${type}`);
  const currentValue = parseInt(input.textContent);
  const maxValue = config.tickets[type].maxQuantity;
  reservesQuantity = globalState.reservesQuantity ||0;

  const capacityCustomer = capacity.customers.find(customer => customer.customer_type_id == customerStringToId(type))

  if (capacityCustomer && capacityCustomer.reserved_spots  >= capacityCustomer.quantity) {
    Swal.fire({
      customClass: {
        confirmButton: "btn btn-primary col-4 mx-1",
        cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
        title: 'h5',
        actions: 'w-100 justify-content-center mt-4',
      },
      buttonsStyling: false,
      icon: 'error',
      title: translations.max_qty
    })
    return
  }

  if (parseInt(capacity.reserved)+reservesQuantity >= parseInt(capacity.service_capacity)) {
    Swal.fire({
      customClass: {
        confirmButton: "btn btn-primary col-4 mx-1",
        cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
        title: 'h5',
        actions: 'w-100 justify-content-center mt-4',
      },
      buttonsStyling: false,
      icon: 'error',
      title: translations.max_qty
    })
    return
  }

  if (currentValue < maxValue) {
    input.textContent = currentValue + 1;
    globalState.quantity = globalState.quantity + 1
    capacity.reserved = parseInt(capacity.reserved) + 1
    if (capacityCustomer) capacityCustomer.reserved_spots += 1
    updateTotals();
  }
}

function decrementTicket(type) {
  const input = document.getElementById(`ticket-${type}`);
  const capacityCustomer = capacity.customers.find(customer => customer.customer_type_id == customerStringToId(type))

  if (parseInt(input.textContent) > 0) {
    input.textContent = parseInt(input.textContent) - 1;
    globalState.quantity = globalState.quantity - 1
    capacity.reserved = parseInt(capacity.reserved) - 1
    if (capacityCustomer) capacityCustomer.reserved_spots -= 1

    updateTotals();
  }
}

function addAdditional(product) {
  const countElement = document.getElementById(`count-${product}`);
  const currentCount = parseInt(countElement.textContent);
  const maxQuantity = config.products[product].maxQuantity;

  if (currentCount < maxQuantity) {
    countElement.textContent = currentCount + 1;
    updateTotals();
  }
}

function removeAdditional(product) {
  const countElement = document.getElementById(`count-${product}`);
  const currentCount = parseInt(countElement.textContent);

  if (currentCount > 0) {
    countElement.textContent = currentCount - 1;
    updateTotals();
  }
}

async function updateTotals() { // <-- Funció original sense descomptes (li fem override més endavant)
  globalState.customer_types = {};
  globalState.additional_products = {};
  var discountTotal = 0;

  const ticketsTotal = calculateTicketTotals();
  const additionalTotal = calculateAdditionalTotals();
  if (window.discountLoaded) {
    discountTotal += await calculateDiscount();  
  }

  updateTotalsDOM(ticketsTotal, additionalTotal, discountTotal);
}
function calculateTicketTotals() {
  let ticketsTotal = 0;

  Object.entries(config.tickets).forEach(([type, data]) => {
    const count = parseInt(document.getElementById(`ticket-${type}`).textContent) || 0;
    const total = count * data.price;
    ticketsTotal += total;

    document.getElementById(`${type}-count`).textContent = count;
    document.getElementById(`${type}-total`).textContent = `${total}€`;

    if (count > 0) {
      globalState.customer_types[type] = {
        customer_type_id: type,
        reserved_spots: count,
        price: total,
        unit_price: data.price
      };
    }
  });

  return ticketsTotal;
}

function calculateAdditionalTotals() {
  let additionalTotal = 0;

  Object.entries(config.products).forEach(([type, data]) => {
    const count = parseInt(document.getElementById(`count-${type}`).textContent) || 0;
    const total = count * data.price;
    additionalTotal += total;

    document.getElementById(`${type}-count`).textContent = count;
    document.getElementById(`${type}-total`).textContent = `${total}€`;

    if (count > 0) {
      globalState.additional_products[data.id] = {
        product_id: data.id,
        quantity: count,
        price: total,
        unit_price: data.price
      };
    }
  });

  return additionalTotal;
}

function updateTotalsDOM(ticketsTotal, additionalTotal, discountTotal) {

  discountTotal = formatNumber(discountTotal);

  // Actualizar totales generales
  document.getElementById('tickets-total').textContent = `${ticketsTotal}€`;
  document.getElementById('additional-total').textContent = `${additionalTotal}€`;
  document.getElementById('final-total').textContent = `${ticketsTotal + additionalTotal}€`;
  document.getElementById('final-total-modal').textContent = `${ticketsTotal + additionalTotal}€`;
  document.getElementById('final-total').textContent = `${ticketsTotal + additionalTotal - discountTotal}€`;
  document.getElementById('final-total-modal').textContent = `${ticketsTotal + additionalTotal - discountTotal}€`;
  document.getElementById('discount-total').textContent = `-${discountTotal}€`;

  if (discountTotal !== 0) {
    document.getElementById("discount").classList.remove("d-none");
  } else {
    document.getElementById("discount").classList.add("d-none");
  }

  // Guardar el total en globalState
  globalState.total_price = ticketsTotal + additionalTotal - discountTotal;
}


function bookNow() {
  if (!globalState.reserve_time || !globalState.booking_end_time) {
    alert(translations2.no_date_selected);
    return;
  }
  if (Object.keys(globalState.customer_types).length === 0) {
    alert(translations2.no_tickets_selected);
    return;
  }

  if (isLoggedIn) {
    let timerInterval;

    Swal.fire({
      customClass: {
        confirmButton: "btn btn-primary col-4 mx-1",
        cancelButton: "btn btn-outline-light border border-3 px-3 col-4 mx-1",
        title: 'h5',
        actions: 'w-100 justify-content-center mt-4',
      },
      buttonsStyling: false,
      title: 'Será redirigido a la pasarela de pago',
      timer: 3000,
      timerProgressBar: true,
      didOpen: () => {
        Swal.showLoading();
      },
      willClose: () => {
        clearInterval(timerInterval);
      }
    }).then((result) => {
      if (result.dismiss === Swal.DismissReason.timer) {
        paymentRequest()
      }
    });
  } else {
    const modalElement = $('#checkoutModal')[0];
    const checkoutModal = new bootstrap.Modal(modalElement, { backdrop: true, size: 'fullscreen' });
    checkoutModal.show();
  }

}

function getCapacity() {
  const id = globalState.event_id;
  const date = moment(globalState.reserve_time).utc().format('YYYY-MM-DDTHH:mm');

  return new Promise((resolve, reject) => {
    $.ajax({
      url: `${API_URL}/services/${id}/capacity/date/${date}`,
      type: 'GET',
      success: function (response) {
        resolve(response.data); // Just return the data
      },
      error: function (xhr, status, error) {
        reject(error);
      }
    });
  });
}

function updateDiscountSpan(type, amount) {
  const discountSpan = document.getElementById(`${type}-discount`);
  const discountWrapper = document.getElementById(`${type}-dis`);

  if (discountSpan && discountWrapper) {
    if (amount > 0) {
      discountSpan.textContent = `-${amount.toFixed(2)}€`;
      discountWrapper.classList.remove("d-none");
    } else {
      discountSpan.textContent = '';
      discountWrapper.classList.add("d-none");
    }
  }
}

function formatNumber(num) {
  // Convert to string and split on decimal point
  const parts = num.toString().split('.');

  // Check if there are decimals and if length is greater than 2
  if (parts.length === 2 && parts[1].length > 2) {
    return Number(num).toFixed(2);
  }

  // Otherwise, return the number as is (or as string)
  return num;
}
