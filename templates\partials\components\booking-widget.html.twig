{# <div>
  <p>Discounts:</p>
    <pre>
        {{ discountData | json_encode|raw }}
    </pre>
</div> #}


<div class="container my-5">
    <div class="border rounded-4 p-4 bg-white shadow-sm">
        <div class="row">
            {# Sección de selección de fecha y hora #}
            <div class="col-12 col-xl-3">
              <h5 class="mb-4 border-bottom border-1 color-muted pb-1">{{currentLangLabels.date_label}}</h5>
                <div id="datetimepicker2" class="mb-3 mt-4 d-flex justify-content-center w-100"></div>
                <div id="available-hours-2" class="row g-3 text-center"></div>
                {# <div id="lastspots"class = "text-primary mt-2"><h5>{{currentLangLabels.last_tickets}}</h5></div> #}
                <p class="message1 " style="display: none; color:red;">
                    {{ currentLangLabels.last_tickets }}
                </p>
                 <p class="message2" style="display: none; color:red;">
                    {{ currentLangLabels.no_spots_this }}
                </p>
            </div>
            {# Sección de selección de plazas #}
            <div class="col-12 col-xl-3 border-start border-muted">
                <h5 class="m-4 border-bottom border-1 color-muted pb-1">Tickets</h5>
                {% for ticket in eventTickets %}
                <div class="card mb-3 shadow-sm">
                    <div class="card-body d-flex align-items-center px-3 py-1 ">
                        <span class="text-secondary me-auto">{{ ticket.display_name }}</span>
                        <div class="ticket-controls d-flex align-items-center mx-auto">
                            <button class="btn btn-outline-secondary border-0"
                                    type="button"
                                    onclick="decrementTicket('{{ ticket.name }}')"
                                    data-ticket-id="{{ ticket.id }}">−</button>
                            <span id="ticket-{{ ticket.name }}"
                                  class="mx-2"
                                  data-max-quantity="{{ product.max_quantity }}">0</span>
                            <button class="btn btn-outline-secondary border-0"
                                    type="button"
                                    onclick="incrementTicket('{{ ticket.name }}')"
                                    data-ticket-id="{{ ticket.id }}">+</button>
                        </div>
                        <span class="ms-auto"><strong>{{ ticket.price | number_format(2) }} €</strong></span>
                    </div>
                </div>
                {% endfor %}
            </div>

            {# Sección de productos adicionales #}
            <div class="col-12 col-xl-3 border-start border-muted">
                <h5 class="mb-4 border-bottom border-1 color-muted pb-1">Extras</h5>
                {% for product in additionalProducts %}
                    <div class="card mb-3 shadow-sm">
                        <div class="card-body">
                            <h6 class="card-title">{{ product.name }}</h6>
                            <small class="text-muted d-block">{{ product.description }}</small>
                            <hr class="my-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <button class="btn btn-outline-secondary border-0"
                                            onclick="removeAdditional('{{ product.name }}')"
                                            data-product-id="{{ product.id }}">-</button>
                                    <span id="count-{{ product.name }}"
                                          class="mx-2"
                                          data-max-quantity="{{ product.max_quantity }}">0</span>
                                    <button class="btn btn-outline-secondary border-0"
                                            onclick="addAdditional('{{ product.name }}')"
                                            data-product-id="{{ product.id }}">+</button>
                                </div>
                                <span class="text-secondary text-bold fw-bold">{{ product.price }}€</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            {# Sección de resumen de precio #}
            <div class="col-12 col-xl-3 border-start border-muted">
                <h5 class="mb-4 border-bottom border-1 color-muted pb-1">{{currentLangLabels.bill_label}}</h5>            
                <div class="card">
                    <div class="card-body">
                        {# Desglose de tickets #}
                        <h6 class="mb-3">Tickets</h6>
                        <div id="tickets-breakdown" class="ms-3 mb-3">
                            {% for ticket in eventTickets %}
                                <div class="d-flex justify-content-between small">
                                    <span>{{ ticket.display_name }} (<span id="{{ ticket.name }}-count">0</span>x{{ ticket.price | number_format(2) }}€)</span>
                                    <span id="{{ ticket.name }}-total">0€</span>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="d-flex justify-content-between fw-bold mb-3">
                            <span>Subtotal Tickets</span>
                            <span id="tickets-total">0€</span>
                        </div>

                        {# Desglose de productos adicionales #}
                        <h6 class="mb-3">Extras</h6>
                        <div id="additional-breakdown" class="ms-3 mb-3">
                            {% for product in additionalProducts %}
                                <div class="d-flex justify-content-between small">
                                    <span>{{ product.name }} (<span id="{{ product.name }}-count">0</span>x{{ product.price }}€)</span>
                                    <span id="{{ product.name }}-total">0€</span>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="d-flex justify-content-between fw-bold mb-3">
                            <span>Subtotal Extras</span>
                            <span id="additional-total">0€</span>
                        </div>
                        
                        <div id="discount" class="d-none">
                            <h6 id="discount-title" class="mb-2">{{currentLangLabels.discount}}</h6>
                            <div id="discount-breakdown" class="ms-3 mb-2">
                                <div id="adult-dis" class="d-flex justify-content-between small d-none">
                                        <span id="adult_name">{{currentLangLabels.adult_label}}</span>
                                        <span id="adult-discount">-0€</span>
                                </div>
                                <div id="child-dis" class="d-flex justify-content-between small d-none">
                                    <span id="child_name">{{currentLangLabels.child_label}}</span>
                                    <span id="child-discount">-0€</span>
                                </div>
                                <div id="senior-dis" class="d-flex justify-content-between small d-none">
                                    <span id="senior_name">{{currentLangLabels.senior_label}}</span>
                                    <span id="senior-discount">-0€</span>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between fw-bold mb-3">
                                <span>Subtotal {{currentLangLabels.discount}}</span>
                                <span id="discount-total">-0€</span>
                            </div>
                        </div>

                        {# Total final #}
                        <div class="d-flex justify-content-between fw-bold fs-5 mt-4 pt-3 border-top">
                            <span>Total</span>
                            <span id="final-total">0€</span>
                        </div>
                    </div>
                </div>            
            </div>
        </div>
        {# Botón de reserva y cupón #}
        <div class="mt-5 d-block d-md-flex justify-content-between">

            <div>
                <div id="coupon-section" class="row g-3 align-items-end d-none">
                    <div class="col-md-auto">
                        <input type="text" class="form-control" id="coupon-code" placeholder="{{currentLangLabels.placeholder_coupon}}">
                    </div>

                    <div class="col-md-auto d-flex align-items-end">
                        <button id="apply-coupon-btn" onclick="applyCoupon()" class="btn btn-primary w-100">
                            {{currentLangLabels.cupon}}
                        </button>
                    </div>
                    <div class="d-flex align-items-center" style="width: 150px;">
                        <div class="text-start w-100">
                            <span id="invalid-cupon" class="d-block m-auto text-secondary fs-4 ms-2 my-auto " ></span>
                        </div>
                    </div>
                    <div class="col-auto d-flex flex-column align-items-end">
                        <span id="applied-coupon" class="d-none text-muted fs-4 ms-2"></span>
                    </div>
                </div>

                <!-- DISCOUNT SECTION -->
                <div class="d-flex flex-column">
                    <div id="discount-section" class="d-none border-start border-primary border-3 ps-3 fw-bold text-secondary small">
                    </div>
                </div>
                <!-- MESSAGE SECTION -->
                <div class="mt-3">
                    <span class="text-primary fs-4">✌ {{currentLangLabels.accumulative_discount_warning}}</span>
                </div>
            </div>


            <!-- Botón de reserva alineado a la derecha -->
            <div class="ms-auto mt-5 mt-md-0">
                <button id="book-now-btn" onclick="bookNow()" class="btn btn-primary btn-lg px-5 w-100 w-md-auto">
                    <i class="fa-solid fa-ticket me-2"></i>{{currentLangLabels.book_now}}
                </button>
            </div>
        </div>
    </div>

    <div id="selected-datetime" class="mt-4 p-3 border border-warning rounded bg-light text-warning-emphasis">
        <p class="mb-0">
            ⚠️ {{currentLangLabels.date_range}}
        </p>
    </div>
</div>
</div>


<script>
   const translations2 = {
    no_date_selected: "{{ currentLangLabels.date_range }}",
     no_tickets_selected: "{{ currentLangLabels.no_tickets_selected }}"
   };
 </script>

<script>
  const translations = {
    adulto: "{{ currentLangLabels.adult_label }}",
    nino: "{{ currentLangLabels.child_label }}",
    jubilado: "{{ currentLangLabels.senior_label }}",
    de: "{{ currentLangLabels.from }}",
    del_ticket: "{{ currentLangLabels.from_ticket }}",
    al_ticket: "{{ currentLangLabels.to_ticket }}",
    discount_available: "{{ currentLangLabels.discount_available }}",
    congratulations: "{{ currentLangLabels.congrat }}",
    reserv: "{{ currentLangLabels.reserv }}",
    descu: "{{ currentLangLabels.descu }}",
    compr: "{{ currentLangLabels.compr }}",
    sig: "{{ currentLangLabels.sig }}",
    des: "{{ currentLangLabels.des }}",
    date: "{{ currentLangLabels.date }}",
    to: "{{ currentLangLabels.to }}",
    congrat: "{{ currentLangLabels.congrat }}",
    max_qty: "{{ currentLangLabels.max_qty }}"

  };
</script>

<script>

    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    async function initScripts() {
        let discountCupons=[];
        let discountsCapacity=[];
        const scriptsUrl = '/assets/js/bookingWidget/';

        // Wait for booking_widget.js to load first
        await loadScript(scriptsUrl + 'booking_widget.js');
        window.discountLoaded = true;
        if(!isRelated){
            if(discountData){
                /* switch (discountData.tableName) {
                    case 'discount_capacity':
                        loadScript(scriptsUrl + 'discount_capacity.js');
                        break;
                    case 'discount_cupon':
                        loadScript(scriptsUrl + 'discount_cupon.js');
                        break;
                    case 'discount_customer_type':
                        loadScript(scriptsUrl + 'discount_customer_type.js');
                        break;
                    case 'discount_date':
                        loadScript(scriptsUrl + 'discount_date.js');
                        break;
                    case 'discount_related':
                        window.discountLoaded = false;
                        break;
                    default:
                        break;
                } */

                Object.entries(discountData.data).forEach(([key, value]) => {
                   if(value.discount_table_name === 'discount_capacity'){
               
                    discountsCapacity.push(value);
                    window.discountsCapacity=discountsCapacity;
                   }
                    if(value.discount_table_name === 'discount_cupon'){
                        discountCupons.push (value),
                        window.discountCupons = discountCupons;
                    }
                    if(value.discount_table_name === 'discount_customer_type'){
                       // console.log("discount_customer_type exist")
                       // loadScript(scriptsUrl + 'discount_customer_type.js');
                    }
                    if(value.discount_table_name === 'discount_date'){
                       // window.discountDate = value;
                       // console.log("discount_date exist")
                       // loadScript(scriptsUrl + 'discount_date.js');
                    }
                });

                if(discountCupons.length>0){
                    loadScript(scriptsUrl + 'discount_cupon.js').then(() => {
                        if(window.totalCuponDiscount!== undefined){
                         //   console.log("MAX CUPON discount")
                          //  console.log(window.totalCuponDiscount)
                        }
                    }).catch(error => {
                        console.error('Error loading discount_cupon.js:', error);
                    });
                }else{
                    console.log("NO cupons")
                }
                if(discountsCapacity.length>0){
                    loadScript(scriptsUrl + 'discount_capacity.js').then(() => {
                        showCapacityDiscount();
                    }).catch(error => {
                        console.error('Error loading discount_capacity.js:', error);
                    });
                }
            }else{               
                window.discountLoaded = false;
                console.log('Sin descuento o descuento no reconocido.');
            }
        }else{
            loadScript(scriptsUrl + 'discount_related.js');
        }
    }

    let globalState = {
        event_id: 0,
        total_price: 0,
        reserve_time: "",
        booking_end_time: "",
        customer_types: {},
        additional_products: {},
        quantity: 0
    };

    let capacity = {
        available_spots: 0,
        max_spots: 0,
        id: globalState.event_id,
        reserved: 0,
        service_capacity: 0,
        customers: []
    }

    let currentLanguage = '{{ currentLang }}';
    let isRelated = '{{isValidKey}}';
    let relatedDiscount = '{{relatedDiscount}}';
    let relatedDiscountType = '{{relatedDiscountType}}';
    let relatedDiscountId = parseInt("{{relatedDiscountId}}") || 0;
    let urlStart = '{{ startSelected }}';
    let urlEnd = '{{ endSelected }}';
    const config = {
        tickets: {
            {% for ticket in eventTickets %}
            '{{ ticket.name }}': {
                id: {{ ticket.id }},
                price: {{ ticket.price }},
                maxQuantity: {{ ticket.max_quantity }}
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        },
        products: {
            {% for product in additionalProducts %}
            '{{ product.name }}': {
                price: {{ product.price }},
                id: {{ product.id }},
                maxQuantity: {{ product.max_quantity }}
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        }
    };

    const discountData = {{ discountData | json_encode | raw }};
    const capacityData = {{ capacityData | json_encode | raw }};

    initScripts().catch(error => {
        console.error('Error initializing scripts:', error);
    });
</script>

<script>
    // Calendari
    window.onload = function () {

        const serviceString = {{ service|json_encode|raw }};
        const serviceBookings = {{serviceBookings|json_encode|raw }};    
        const env = JSON.parse({{var_export(env|json_encode)|raw }});

        const service = serviceString;

        globalState.event_id = service.id;

        let today = moment();
        

        let endDate = service['end_date']
            ? moment(espTimeZoneFormatter(service['end_date'], DATE_FORMAT))
            : null;
       
        // Máximo de días que se puede reservar
        let maxReserveDays = service['max_reserve_days'];

        
        // Sumar +1 para incluir el día de endDate en el rango permitido
        let daysUntilEnd = endDate ? endDate.diff(today, 'days') + 1 : maxReserveDays;

        // Nos aseguramos de que no sea negativo
        let validDays = Math.min(maxReserveDays, Math.max(0, daysUntilEnd));

        let maxDate = today.clone().add(validDays, 'days').toDate();
       

        let startDate = moment(espTimeZoneFormatter(service['start_date'], DATE_FORMAT))
                

                /*console.log("service['start_date']", service['start_date']);
        let startDate = moment(service['start_date'])
                .toDate(); // Luego convierte a Date*/
        
       
        let clonedStartDate = moment(startDate).clone()
        let enabledDates = [];
        let availableBookings = {};
        let price = service.price
        let availableQty = 0;
        let selectedDates = {};
        let selectedBtnTimeElement = '';
        let invalidDate = false;

        $(document).ready(function () {

            do { //while max date is not surpassed
                for (const interval of service.intervals) {
                    const date = clonedStartDate.clone().add(interval.day, 'days').toDate();
                    if (moment(date).isSameOrBefore(maxDate, 'day')) {
                        enabledDates.push(date)
                        if (!availableBookings[moment(date).format(DATE_FORMAT)] || availableBookings[moment(date).format(DATE_FORMAT)].length === 0)
                            availableBookings[moment(date).format(DATE_FORMAT)] = []

                        availableBookings[moment(date).format(DATE_FORMAT)].push({
                            startHour: espTimeZoneFormatter(interval.start_hour, TIME_FORMAT),
                            endHour: espTimeZoneFormatter(interval.end_hour, TIME_FORMAT)
                        })
                    }
                }
                clonedStartDate = clonedStartDate.add(service.periodicity, 'days')
            } while (clonedStartDate.isSameOrBefore(maxDate, 'day'))

            //If start date starts before today we disable the days
            startDate = moment(startDate).isSameOrAfter(moment()) ? startDate : moment().format(DATE_FORMAT)
          
            // TODO: If query params, set the date to the calendar 
            // const params = new URLSearchParams(window.location.search);
            // const start = params.get('start');
            // const end = params.get('end');  

            // let defaultDateValue;
            // if (start) {
            //     formattedDate = moment(start).format('YYYY-MM-DD');
            // } 
            
            // console.log("defaultDateValue", formattedDate)

            let tempusDominusOptions = getCalendarOptions(
                moment(startDate, "YYYY-MM-DD").toDate(),
                moment(maxDate, "YYYY-MM-DD").toDate(),
                enabledDates,
                '{{ currentLang }}',
                moment().format('YYYY-MM-DD hh:mm:ss')
            )

            // Configurar lunes como primer día de la semana
            tempusDominusOptions.localization.startOfTheWeek = 1;


            let tempus2 = new tempusDominus.TempusDominus($('#datetimepicker2')[0], tempusDominusOptions)

            tempus2.subscribe(tempusDominus.Namespace.events.change, (event) => {
                $('#available-hours-2').html('');

                const momentDate = moment(event.date);
                if (!momentDate.isValid()) {
                    console.error("Invalid event date:", event.date);
                    return;
                }
                const dateWithoutMinutes = momentDate.format(DATE_FORMAT);

                selectedDates = {};
                selectedDates[dateWithoutMinutes] = availableBookings[dateWithoutMinutes];

                if(selectedDates[dateWithoutMinutes]){
                    getAvailableHours(selectedDates);
                    activateAvailableHourBtns();
                }else{
                    invalidDate = true;
                }

                // Wait a bit to make sure the buttons are in the DOM
                setTimeout(() => {
                    // Autoselect if urlStart/urlEnd exist
                    if (urlStart && urlEnd) {
                        const startTime = espTimeZoneFormatter(urlStart.split("T")[1], TIME_FORMAT_NO_SECONDS);
                        const endTime = espTimeZoneFormatter(urlEnd.split("T")[1], TIME_FORMAT_NO_SECONDS);
                        const timeSlot = `${startTime}-${endTime}`;
                        const buttonId = `#btn-time-${startTime.replace(':', '')}`;

                        const btn = $(`${buttonId}[data-date="${dateWithoutMinutes}"][data-time="${timeSlot}"]`);

                        if (btn.length && !btn.hasClass('disabled')) {
                            btn.trigger("click");
                        }
                        urlStart = null;
                        urlEnd = null;
                    }
                }, 200); // Enough delay for DOM buttons to be rendered
            });

            // Set default calendar date based on URL
            if(urlStart ){
                const urlDate = moment(urlStart).format('YYYY-MM-DD');
                tempus2.dates.setValue(new tempusDominus.DateTime(urlDate));
                // Remove existing active classes (optional)
                $('[data-action="selectDay"].day').removeClass('active');

                // Find and activate the matching day
                $(`[data-action="selectDay"].day[data-value="${urlDate}"]`).addClass('active');
            }


            $(document).on("click", ".btn-time", async function () {
                // const id = $(this).data("id");
                selectedBtnTimeElement = $(this);
                bookingSelectedTime = $(this).data("time");
                bookingSelectedDate = $(this).data("date"); 

                $(this).parent().siblings().find('.btn-time').removeClass('active');//remove active class from the other buttons
                const bookedQty = parseInt($(this).attr('data-booked-qty'))
                availableQty = parseInt($(this).attr('data-max-bookings'));

                if (!$(this).hasClass('active')) {
                    $(this).addClass("active")
                    $('#input-qty-to-book').attr('max', availableQty)
                    $('#bookings-left-text').text(`{{ currentLangLabels['bookings_quantity_text'] }}: ${availableQty}`)
                } else {
                    $(this).removeClass('active')
                }

                if (bookingSelectedTime && bookingSelectedDate) {
                    const [startTime, endTime] = bookingSelectedTime.split("-");
                    const startDateTime = new Date(`${bookingSelectedDate}T${startTime}`);
                    const endDateTime = new Date(`${bookingSelectedDate}T${endTime}`);
                 
                    const formattedDate = `${bookingSelectedDate}` + "_" + `${startTime}`+ ":00";
                    let reservesQuantity = 0;
                   
                    for (const booking of serviceBookings) {
                       
                        if(booking.id == service.id && booking.booking_time == formattedDate && booking.status=="paid"){
                           
                            reservesQuantity += booking.quantity;
                        }  
                    }
                    globalState.reservesQuantity = reservesQuantity;
                    // Encontrar el mensaje condicional DENTRO de esta tarjeta específica
                    const message_last_spots = document.querySelector('.message1');
                    const message_no_spots = document.querySelector('.message2');
                    
                    if(reservesQuantity ==  service.capacity){
                        message_no_spots.style.display = 'block';
                    }else if(reservesQuantity > (env.WARNING_CAPACITY_LIMIT_PERCENTAGE * service.capacity) / 100) {
                        message_last_spots.style.display = 'block';
                    } else {
                        message_last_spots.style.display = 'none'; // Ocultar si no cumple la condición
                    }
                  
            
                    
                    const options = {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                    };

                    const formattedStart = startDateTime.toLocaleString('{{ currentLang }}', options);
                    const formattedEnd = endDateTime.toLocaleString('{{ currentLang }}', options);
                    
                    $('#selected-datetime').text(`📅 {{ currentLangLabels.selected_date}}: ${formattedStart} - ${formattedEnd}`);

                    globalState.reserve_time = startDateTime;
                    globalState.booking_end_time = endDateTime;

                    capacity = await getCapacity()
/*
                    if(discountData.tableName === "discount_capacity"){
                        console.log("estoy aqui")
                        showCapacityDiscount();
                    } */

                    calculateDiscount();
                    updateTotals();


                }
            })
        });

        function getAvailableHours(selectedDate) {
            const day = Object.keys(selectedDate)[0]
            const intervals = selectedDate[day]

            for (const interval of intervals) {
                const startHour = moment(interval.startHour, TIME_FORMAT).format('HH:mm')
                const endHour = moment(interval.endHour, TIME_FORMAT).format('HH:mm')
                const time = `${startHour}-${endHour}`
                $('#available-hours-2').append(`
                    <div class="col-6 col-md">
                        <button id="btn-time-${startHour.replace(':', '')}" data-date="${day}" data-time="${time}" data-max-bookings="5"
                            class="btn btn-time w-100 fs-4 disabled">${startHour} - ${endHour}</button>
                    </div>

                `)
            }
        }

        function checkDayCapacity(id, availableDates, type) {
            if (Object.keys(availableDates).length > 0) {
                const date = Object.keys(availableDates)[0]
                const startDate = `${date} ${availableDates[date][0]['startHour']}`    
                const endDate = `${date} ${availableDates[date][availableDates[date].length - 1]['endHour']}`

                $.get(`/api/getBookings.php?type=${type}&start-date=${utcTimeZoneFormatter(startDate, DATETIME_FORMAT)}&end-date=${moment(endDate, DATETIME_FORMAT).format(DATETIME_FORMAT)}&id=${id}`)
                    .done((response) => {
                        let data = {}
                        try {
                            data = JSON.parse(response);
                            let childrenBtn = []
                            $('#available-hours-2').children().map(function () {
                                $(this).children().map(function () {
                                    childrenBtn.push($(this).attr('id'))
                                    $(this).removeClass('disabled')
                                })
                            });

                            for (const childrenBtnElement of childrenBtn) {
                                const btnElement = $(`#${childrenBtnElement}`)
                                const btnStartHour = btnElement.attr('data-time').split('-')[0]
                                const btnDate = btnElement.attr('data-date')
                                const date = utcTimeZoneFormatter(`${btnDate}_${btnStartHour}`, DATETIME_FORMAT)
                                const dayBookings = data['bookings'].filter(function (booking) {
                                    return booking.booking_time === date;
                                });
                                let totalCapacity = service.capacity
                                for (const dayBooking of dayBookings) {
                                    totalCapacity -= dayBooking.quantity
                                }

                                const bookedQty = service.capacity - totalCapacity
                                let availableQty = service.capacity - bookedQty
                                if (availableQty > service.max_reserves) availableQty = service.max_reserves

                                btnElement.attr('data-booked-qty', bookedQty)
                                btnElement.attr('data-max-bookings', availableQty)
                                
                                $('#input-qty-to-book').attr('max', bookedQty)
                                $('#bookings-left-text').text(`{{ currentLangLabels.bookings_quantity_text }}: ${availableQty}`)

                                if (totalCapacity <= 0) {
                                    btnElement.addClass('disabled')

                                } else {
                                    btnElement.removeClass('disabled')
                                }
                            }
                        } catch (e) {
                            console.log('ERROR:', e)
                        }
                    })
                    .fail((e) => {
                        console.log('Error:', e);
                    })

            }
        }

        function activateAvailableHourBtns() {
            $('#available-hours-2').children().map(function () {
                $(this).children().map(function () {
                    $(this).removeClass('disabled')
                })
            });
        }
    };
</script>
