async function showCapacityDiscount() {
    try {
        // Just prepare the discount section - calculateDiscount() will handle the actual display
        const div = document.getElementById("discount-section");
        if (div) {
            // Clear any existing content
            const spans = div.querySelectorAll("span");
            if (spans) {
                spans.forEach(span => span.remove());
            }
            // Make sure the section is visible
            div.classList.remove("d-none");
        }
    } catch (error) {
        console.error("Error preparing discount section:", error);
    }
}

async function calculateDiscount(reservesQuantity) {
    let totalDiscount = 0;
    let totalTickets = 0;
    // let capacity;
    let reservePosition = 0;
    let reserveLeft = 0;

    // If globalState.reserve_time is null or is not created, return 0 discount
    if (!globalState.reserve_time) {
        return totalDiscount;
    }
    /*try {
        capacity = await getCapacity();
    } catch (error) {
        console.error('Failed to fetch capacity:', error);
        return totalDiscount;
    }*/

    Object.entries(config.tickets).forEach(([type]) => {
        const count = parseInt(document.getElementById(`ticket-${type}`).textContent) || 0;
        totalTickets += count;
    });
    const totalPrice = calculateTicketTotals();
    // reservePosition = parseInt(capacity.reserved) + 1;
    reservePosition = parseInt(capacity.reserved);

    const discountTable = discountData.data.find(table => table.discount_table_name === "discount_capacity");
    const currentRange = discountTable?.items.find(row =>
        reservePosition >= row.from && reservePosition <= row.to
    );
    if (!currentRange) {
        return totalDiscount;
    }

    reserveLeft = currentRange.to - reservePosition- reservesQuantity;
    console.log(reserveLeft, "RESERVE LEFT")
    console.log(reservesQuantity, "RESERVES QUANTITY")
    console.log(reservePosition, "RESERVE POSITION")
    const discountSection = document.querySelector("#discount-section");

    // Make sure the discount section is visible
    discountSection.classList.remove("d-none");

    // Clear any existing content
    discountSection.innerHTML = "";

    // If valid number of tickets
    if (0<= reserveLeft) {
        // Remove alert if it exists
        const existingAlert = discountSection.querySelector(".alert-span");
        if (existingAlert) {
            existingAlert.remove();
        }

        // Show discount message
        const span = document.createElement("span");
        if (reserveLeft > 0)
        span.textContent = ` ${translations.congrat} ${reserveLeft} ${translations.reserv} ${translations.descu} ${currentRange.discount}${currentRange.discount_type}.`;
        discountSection.appendChild(span);

        // Apply discount
        switch (currentRange.discount_type) {
            case '%':
                totalDiscount = totalPrice * (currentRange.discount / 100);
                break;
            case '€':
            default:
                totalDiscount = currentRange.discount *totalTickets;
                break;
        }
    } else {
        // Create Bootstrap alert
        const alertDiv = document.createElement("div");
        alertDiv.className = "alert alert-danger alert-dismissible fade show py-1 px-2 small mb-0 align-items-center"; // Bootstrap alert classes
        alertDiv.role = "alert";
        alertDiv.innerHTML = `
        Máximo de tickets con descuento sobrepasado.
        `;

        // Append the alert to the discount section (or any desired parent element)
        discountSection.appendChild(alertDiv);

        // No discount if over capacity
        totalDiscount = 0;
    }

    globalState.discount = currentRange.discount_id;

    return totalDiscount;
}