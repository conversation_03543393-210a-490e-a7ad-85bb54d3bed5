async function showCapacityDiscount() {
    try {
        console.log("showCapacityDiscount: Starting function");

        const div = document.getElementById("discount-section");
        console.log("showCapacityDiscount: discount-section element:", div);

        const spans = div.querySelectorAll("span");
        if (spans) {
            spans.forEach(span => span.remove());
        }

        const capacity = await getCapacity();
        console.log("showCapacityDiscount: capacity data:", capacity);

        // const reservePosition = parseInt(capacity.reserved) + 1;
        const reservePosition = parseInt(capacity.reserved);
        console.log("showCapacityDiscount: reservePosition:", reservePosition);

        console.log("showCapacityDiscount: discountData:", discountData);
        const discountTable = discountData.data.find(table => table.discount_table_name === "discount_capacity");
        console.log("showCapacityDiscount: discountTable:", discountTable);

        const currentRange = discountTable?.items.find(row =>
            reservePosition >= row.from && reservePosition <= row.to
        );
        console.log("showCapacityDiscount: currentRange:", currentRange);

        if (!currentRange) {
            console.log("showCapacityDiscount: No currentRange found, exiting");
            return;
        }

        const reserveLeft = currentRange.to - reservePosition;
        console.log("showCapacityDiscount: reserveLeft:", reserveLeft);

        document.querySelector("#discount-section").classList.remove("d-none");

        const span = document.createElement("span");
        if (reserveLeft > 0) {
            console.log("showCapacityDiscount: Creating span with message");
            span.textContent = ` ${translations.congrat} ${reserveLeft} ${translations.reserv} ${translations.descu} ${currentRange.discount}${currentRange.discount_type}.`;
            document.querySelector("#discount-section").appendChild(span);
            console.log("showCapacityDiscount: Span added to DOM");
        } else {
            console.log("showCapacityDiscount: reserveLeft <= 0, not showing message");
        }
    } catch (error) {
        console.error("Error fetching discount info:", error);
    }
}

async function calculateDiscount(reservesQuantity) {
    let totalDiscount = 0;
    let totalTickets = 0;
    // let capacity;
    let reservePosition = 0;
    let reserveLeft = 0;

    // If globalState.reserve_time is null or is not created, return 0 discount
    if (!globalState.reserve_time) {
        return totalDiscount;
    }
    /*try {
        capacity = await getCapacity();
    } catch (error) {
        console.error('Failed to fetch capacity:', error);
        return totalDiscount;
    }*/

    Object.entries(config.tickets).forEach(([type]) => {
        const count = parseInt(document.getElementById(`ticket-${type}`).textContent) || 0;
        totalTickets += count;
    });
    const totalPrice = calculateTicketTotals();
    // reservePosition = parseInt(capacity.reserved) + 1;
    reservePosition = parseInt(capacity.reserved);

    const discountTable = discountData.data.find(table => table.discount_table_name === "discount_capacity");
    const currentRange = discountTable?.items.find(row =>
        reservePosition >= row.from && reservePosition <= row.to
    );
    if (!currentRange) {
        return totalDiscount;
    }

    reserveLeft = currentRange.to - reservePosition- reservesQuantity;
    const discountSection = document.querySelector("#discount-section");

    // Clear any existing content
    discountSection.innerHTML = "";

    // If valid number of tickets
    if (0<= reserveLeft) {
        // Remove alert if it exists
        const existingAlert = discountSection.querySelector(".alert-span");
        if (existingAlert) {
            existingAlert.remove();
        }

        // Show discount message
        const span = document.createElement("span");
        if (reserveLeft > 0)
        span.textContent = ` ${translations.congrat} ${reserveLeft} ${translations.reserv} ${translations.descu} ${currentRange.discount}${currentRange.discount_type}.`;
        discountSection.appendChild(span);

        // Apply discount
        switch (currentRange.discount_type) {
            case '%':
                totalDiscount = totalPrice * (currentRange.discount / 100);
                break;
            case '€':
            default:
                totalDiscount = currentRange.discount *totalTickets;
                break;
        }
    } else {
        // Create Bootstrap alert
        const alertDiv = document.createElement("div");
        alertDiv.className = "alert alert-danger alert-dismissible fade show py-1 px-2 small mb-0 align-items-center"; // Bootstrap alert classes
        alertDiv.role = "alert";
        alertDiv.innerHTML = `
        Máximo de tickets con descuento sobrepasado.
        `;

        // Append the alert to the discount section (or any desired parent element)
        discountSection.appendChild(alertDiv);

        // No discount if over capacity
        totalDiscount = 0;
    }

    globalState.discount = currentRange.discount_id;

    return totalDiscount;
}