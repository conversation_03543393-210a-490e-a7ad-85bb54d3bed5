<?php
require_once __DIR__ . '/../vendor/autoload.php';

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Server\RequestHandlerInterface as RequestHandler;
use Slim\Factory\AppFactory;
use Slim\Handlers\Strategies\RequestResponseArgs;
use Slim\Routing\RouteCollectorProxy;
use Slim\Views\Twig;
use Slim\Views\TwigMiddleware;
use Src\Api\Activities\ActivitiesApi;
use Src\Api\Activities\Criteria\ActiveActivities;
use Src\Api\Activities\Criteria\ActivityBookingByCustomer;
use Src\Api\Activities\Criteria\ActivityById;
use Src\Api\Activities\Criteria\LastActivities;
use Src\Api\Categories\CategoriesApi;
use Src\Api\Categories\Criteria\CategoryById;
use Src\Api\News\Criteria\LastNews;
use Src\Api\News\Criteria\NewsById;
use Src\Api\News\NewsApi;
use Src\Api\Legals\LegalsApi;
use Src\Api\Services\Criteria\ByActiveServiceCategories;
use Src\Api\Services\Criteria\ByActiveServices;
use Src\Api\Services\Criteria\LastServices;
use Src\Api\Services\Criteria\ServiceBookingByCustomer;
use Src\Api\Services\Criteria\ServiceById;
use Src\Api\Services\Criteria\ServiceBookingsByDateAndId;
use Src\Api\Services\Criteria\ServiceCategoryById;
use Src\Api\Services\ServicesApi;
use Src\Api\Translations\ConfigSelector;
use Src\Api\Translations\TranslationsApi;
use Src\Container\Container;
use Src\Criteria\Common\ByLangCode;
use Src\Criteria\Criteria;
use Src\Criteria\Filter;
use Src\Criteria\FilterOperators;
use Src\Criteria\Order;
use Src\TwigExtensions\TwigDateExtension;
use Src\TwigExtensions\TwigJsonExtension;
use Src\TwigExtensions\TwigUtils;
use Src\utils\TimeZoneFormatter;
use Symfony\Contracts\HttpClient\HttpClientInterface;

// Loading environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');
$dotenv->load();

Container::createWithDefaults();

$app = AppFactory::create();

$routeCollector = $app->getRouteCollector();
$routeCollector->setDefaultInvocationStrategy(new RequestResponseArgs());

// Create Twig
$twig = Twig::create(__DIR__ . '/../templates', ['cache' => false]);
$extensions = [
    new TwigJsonExtension(),
    new TwigDateExtension(),
    new TwigUtils(),
];
foreach ($extensions as $extension) {
    $twig->addExtension($extension);
}
// Add Twig-View Middleware
$app->add(TwigMiddleware::create($app, $twig));

function getConfigData(HttpClientInterface $client, string $language): array
{
    $translationsApi = new TranslationsApi($client);

    $config = $translationsApi->get(Criteria::empty())['data'];

    $defaultLang = $config['default_language'];
    $languages = ConfigSelector::getLanguages($config);

    $currentLangLabels = ConfigSelector::getLangLabels($config, $language);
    if (count($currentLangLabels) == 0) {
        $currentLangLabels = ConfigSelector::getLangLabels($config, $defaultLang);
    }

    return [
        'pages' => [
            [
                'url' => 'home',
                'translationKey' => 'home'
            ],
            /*[
                'url' => 'categories',
                'translationKey' => 'categories'
            ],*/
            [
                'url' => 'events',
                'translationKey' => 'services'
            ],
            // [
            //     'url' => 'news',
            //     'translationKey' => 'news'
            // ],
            // [
            //     'url' => 'https://www.clubhotelaguamarina.com/info#checkin-info-general',
            //     'translationKey' => 'information'
            // ],
            /*[
                'url' => 'contact',
                'translationKey' => 'contact'
            ],*/
            // [
            //     'url' => 'virtual-tour',
            //     'translationKey' => 'virtual_tour'
            // ],
            // [
            //     'url' => 'culture',
            //     'translationKey' => 'culture'
            // ],
        ],
        'env' => $_ENV,
        'cookie' => $_COOKIE,
        'currentLang' => $language,
        'defaultLang' => $defaultLang,
        'languages' => $languages,
        'currentLangLabels' => $currentLangLabels,
    ];
}


$app->group('/api', function (RouteCollectorProxy $group) {
    $group->delete('/logout.php', function (Request $request, Response $response) {
        return $response
//            ->withAddedHeader('Set-Cookie', "accessToken=; Max-Age=0; Path=/; SameSite=Strict; Secure")
            ->withAddedHeader('Set-Cookie', "accessToken=; Max-Age=0; Path=/; SameSite=Strict;")
//            ->withAddedHeader('Set-Cookie', "refreshToken=; Expires=0; Path=/; SameSite=Strict; Secure");
            ->withAddedHeader('Set-Cookie', "refreshToken=; Expires=0; Path=/; SameSite=Strict;");
    });

    $group->post('/refreshToken.php', function (Request $request, Response $response) {
        $parsedBody = $request->getParsedBody();

        if (empty($parsedBody['token'])) {
            return $response->withStatus(400);
        }

        $accessToken = $parsedBody['token'];
        $url = $_ENV['API_URL'] . '/auth/customer/refresh';
        $body = ['token' => $accessToken];

        // Hacer la petición al backend
        $rawResponse = Container::$client->request('POST', $url, ['json' => $body])->getContent(false);
        $tokensResponse = json_decode($rawResponse, true); // convertir a array

        // Verificamos si se recibió un nuevo token válido
        if (
            isset($tokensResponse['success'], $tokensResponse['data']['access_token']) &&
            $tokensResponse['success'] === true
        ) {
            $accessToken = $tokensResponse['data']['access_token'];
            $expirationTime = gmdate('D, d M Y H:i:s T', time() + 3600); // UTC para Expires

            // Establecer la cookie con el nuevo token
            $response = $response->withHeader(
                'Set-Cookie',
                "accessToken=$accessToken; Expires=$expirationTime; Path=/; SameSite=Strict; HttpOnly"
            );
        }

        $statusCode = isset($tokensResponse['success']) && $tokensResponse['success'] ? 200 : 400;

        $response->getBody()->write(json_encode($tokensResponse));
        return $response
            ->withStatus($statusCode)
            ->withHeader('Content-Type', 'application/json');
    });


    $group->get('/getBookings.php', function (Request $request, Response $response) {
        $query = $request->getQueryParams();
        $type = $query['type'];
        $id = $query['id'];
        $date = $query['date'] ?? "";
        $startDate = $query['start-date'];
        $endDate = $query['end-date'];
        $serviceApi = new ServicesApi(Container::$client);
        $activityApi = new ActivitiesApi(Container::$client);
        if (isset($type) && $type == 'services') {
            $idCriteria =  (new Criteria(
                [
                    new Filter('id', FilterOperators::EQUAL, (int)$id),
                    new Filter('reserve_time', FilterOperators::EQUAL_GREATER, $startDate),
                    new Filter('booking_end_time', FilterOperators::EQUAL_MINOR, $endDate),
                ]
            ));
            $serviceBookings = $serviceApi->getBookings($idCriteria)['data'];
            $response->getBody()->write(json_encode($serviceBookings, true));

            /*$bookings = getServiceBookings($id, $startDate, $endDate);
            $response->getBody()->write(json_encode($bookings, true));*/

        }

        if (isset($type) && $type == 'all-services') {
            $bookings = getServiceBookings($id, $startDate, $endDate);
            $response->getBody()->write(json_encode($bookings, true));
        }

        if (isset($type) && $type == 'activities') {
            $idCriteria =  (new Criteria(
                [
                    new Filter('id', FilterOperators::EQUAL, (int)$id),
                    new Filter('reserve_date', FilterOperators::EQUAL, $date),
                ]
            ));
            $activityBookings = $activityApi->getBookings($idCriteria)['data'];
            $response->getBody()->write(json_encode($activityBookings, true));

            /*$bookings = getActivityBookings($id, $date);
            $response->getBody()->write(json_encode($bookings, true));*/
        }

        if (isset($type) && $type == 'activities-all') {
            $bookings = getActivityBookings($id, $date);
            $response->getBody()->write(json_encode($bookings, true));
        }

        return $response;
    });

    $group->post('/login.php', function (Request $request, Response $response) {
//        echo $request->getBody();
        $contentType = $request->getHeaderLine('Content-Type');
        $parsedBody = $request->getParsedBody();

        if (stripos($contentType, 'application/json') !== false) {
            $raw = $request->getBody()->getContents();
            $parsedBody = json_decode($raw, true);
            $request->getBody()->rewind();
        }
        $requestBody = $parsedBody;
        if (empty($requestBody)) {
            return $response->withStatus(400);
        }

        $body = array(
            'email' => $requestBody['email'],
            'password' => $requestBody['password']
        );

        $url = $_ENV['API_URL'] . '/auth/customer/login';
        $loginResponse = Container::$client->request('POST', $url, ['json' => $body]);
        $tokensResponse = json_decode($loginResponse->getContent(false));
        if (!$tokensResponse->success || count(get_object_vars($tokensResponse->data)) < 1) {
            $response->getBody()->write(json_encode(['message' => $tokensResponse->data->message]));
            return $response->withStatus(400);
        }


        $accessToken = $tokensResponse->data->access_token;
        $refreshToken = $tokensResponse->data->refresh_token;
        $currentTime = time();
        $accessExpirationTime = $currentTime + 3600;
        $refreshExpirationTime = $currentTime + (60 * 60 * 6);

        $response = $response
//            ->withAddedHeader('Set-Cookie', "accessToken=$accessToken; Expires=$accessExpirationTime; Path=/; SameSite=Strict; Secure")
            ->withAddedHeader('Set-Cookie', "accessToken=$accessToken; Expires=$accessExpirationTime; Path=/; SameSite=Strict;")
//            ->withAddedHeader('Set-Cookie', "refreshToken=$refreshToken; Expires=$refreshExpirationTime; Path=/; SameSite=Strict; Secure");
            ->withAddedHeader('Set-Cookie', "refreshToken=$refreshToken; Expires=$refreshExpirationTime; Path=/; SameSite=Strict;");

        $response->getBody()->write(json_encode($tokensResponse));
        return $response->withHeader('Content-Type', 'application/json');
    });

    $group->post('/book.php', function (Request $request, Response $response) {
        $body = $request->getParsedBody();
        $url = $_ENV['API_URL']. '/' . $body['type'] . '/' . $body['id'] . '/bookings';
//        echo $url.$request->getBody();
        $bookingResponse = Container::$client->request('POST', $url, [
            'json' => $body,
            'headers' => [
                'Authorization' => 'Bearer ' . $body['bearerToken']
            ]
        ]);
        $parsedResponse = json_decode($bookingResponse->getContent(false));
        $response->getBody()->write(json_encode($parsedResponse, true));
        return $response;
    });
});

$app->group('/{language}', function (RouteCollectorProxy $group) {
    $group->get('/single-news/{id}[/]', function (Request $request, Response $response, string $language, string $id) {
        $view = Twig::fromRequest($request);
        $newsApi = new NewsApi(Container::$client);
        $config = getConfigData(Container::$client, $language);
        [$singleNews] = $newsApi->get(new NewsById((int)$id))['data'];


        $responseData = [
            ...$config,
            'singleNews' => $singleNews,
        ];

        return $view->render($response, 'pages/news/single_news.html.twig', $responseData);
    });

    $group->get('/bookings[/]', function (Request $request, Response $response, string $language) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');
//        var_dump($config);
        $currentUser = $config['currentUser'];
        $defaultLang = $config['defaultLang'];

        if (!$currentUser) {
            // Redirect to /home
            return $response->withStatus(301)->withHeader('Location', "/$language/home/");
        }

        $servicesApi = new ServicesApi(Container::$client);
        $activitiesApi = new ActivitiesApi(Container::$client);

        $filterLang = new Filter('lang_code', FilterOperators::IN, [$language, $defaultLang]);

        $criteria = new Criteria([new Filter('active', FilterOperators::EQUAL, true), $filterLang]);
        $services = $servicesApi->get($criteria)['data'];
        $activities = $activitiesApi->get($criteria)['data'];
//        Container::$logger->debug('Getting bookings');
        $bookedActivities = $activitiesApi->getBookings(new ActivityBookingByCustomer($currentUser->id))['data'];
        $bookedServices = $servicesApi->getBookings(new ServiceBookingByCustomer($currentUser->id))['data'];
        $currentDate = new DateTime();

//        Container::$logger->debug('Rendering template');
        $responseData = [
            ...$config,
            'services' => $services,
            'activities' => $activities,
            'bookedActivities' => $bookedActivities,
            'bookedServices' => $bookedServices,
            'currentDate' => $currentDate
        ];
        return $view->render($response, 'pages/bookings.html.twig', $responseData);
    });

    $group->get('/news[/]', function (Request $request, Response $response, string $language) {
        $view = Twig::fromRequest($request);
        $newsApi = new NewsApi(Container::$client);

        $config = $request->getAttribute('config');

        $defaultLang = $config['defaultLang'];

        $filterLang = new Filter('lang_code', FilterOperators::IN, [$language, $defaultLang]);
        $currentTime = date('Y-m-d_H:i:s', time());

        $newsCriteria = (new Criteria(
            [new Filter('publish_date', FilterOperators::EQUAL_MINOR, $currentTime)],
            Order::desc('publish_date')
        ))->addFilter($filterLang);
        $news = $newsApi->get($newsCriteria)['data'];
        $responseData = [
            ...$config,
            'news' => $news
        ];

        return $view->render($response, 'pages/news/news.html.twig', $responseData);
    });

    $group->get('/service-categories[/]', function (Request $request, Response $response, string $language) {
        $view = Twig::fromRequest($request);
        $serviceApi = new ServicesApi(Container::$client);
        $config = $request->getAttribute('config');
        $defaultLanguage = $config['defaultLang'];
        $languageFilter = new Filter('lang_code', FilterOperators::IN, [$defaultLanguage, $language]);

        $serviceCategoriesCriteria = (new ByActiveServiceCategories())->addFilter($languageFilter);
        $serviceCategories = $serviceApi->getCategories($serviceCategoriesCriteria)['data'];
        $servicesCriteria = (new ByActiveServices())->addFilter($languageFilter);
        $services = $serviceApi->get($servicesCriteria)['data'];

        $responseData = [
            'currentPage' => 'service-categories',
            ...$config,
            'categories' => $serviceCategories,
            'services' => $services
        ];
        // TODO get service categories by active param and by language
        return $view->render($response, 'pages/services/service_categories.html.twig', $responseData);
    });

    $group->get('/service-categories/{id}[/]', function (Request $request, Response $response, string $language, string $id) {
        $view = Twig::fromRequest($request);
        $servicesApi = new ServicesApi(Container::$client);
        $config = $request->getAttribute('config');
        $defaultLanguage = $config['defaultLang'];

        $langFilter = new Filter('lang_code', FilterOperators::IN, [$language, $defaultLanguage]);
        $category = $servicesApi->getCategories(new ServiceCategoryById((int)$id))['data'];
        $services = $servicesApi->get((new ByActiveServices())->addFilter($langFilter))['data'];

        $responseData = [
            ...$config,
            'category' => $category,
            'services' => $services
        ];

        return $view->render($response, 'pages/services/service_category.html.twig', $responseData);
    });

    $group->get('/services/{id}[/]', function (Request $request, Response $response, string $language, string $id) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');

        $defaultLanguage = $config['defaultLang'];
        $languageFilter = new Filter('lang_code', FilterOperators::IN, [$defaultLanguage, $language]);

        $serviceApi = new ServicesApi(Container::$client);
        [$service] = $serviceApi->get(new ServiceById((int)$id))['data'];

        $categoriesApi = new CategoriesApi(Container::$client);
        $categories = $categoriesApi->get(new Criteria([$languageFilter]))['data'];

        $responseData = [
            'currentService' => $service,
            'categories' => $categories,
            'currentPage' => 'service-categories',
            'id' => $id,
            ...$config
        ];

        return $view->render($response, 'pages/services/service.html.twig', $responseData);
    });

    $group->get('/home[/]', function (Request $request, Response $response, string $language) {
        $view = Twig::fromRequest($request);

        $activitiesApi = new ActivitiesApi(Container::$client);
        $servicesApi = new ServicesApi(Container::$client);
        $newsApi = new NewsApi(Container::$client);
        $categoriesApi = new CategoriesApi(Container::$client);


        // Getting page translations
        $config = getConfigData(Container::$client, $language);
        $defaultLang = $config['defaultLang'];

        // Setting base filter
        $languageFilter = new Filter('lang_code', FilterOperators::IN, [$defaultLang, $language]);

        // Getting last three activities
        $activitiesCriteria = (new LastActivities(3))->addFilter($languageFilter);
        $lastActivities = $activitiesApi->get($activitiesCriteria)['data'];

        // Getting last three services
        $servicesCriteria = (new LastServices(3))->addFilter($languageFilter);
        $lastServices = $servicesApi->get($servicesCriteria)['data'];

        // Getting last three news
        $newsCriteria = (new LastNews(1))->addFilter($languageFilter);
        $lastNews = $newsApi->get($newsCriteria)['data'];

        $categories = $categoriesApi->get(new Criteria([$languageFilter]))['data'];
        $activities = $activitiesApi->get((new ActiveActivities())->addFilter($languageFilter))['data'];

          $eventsCriteria = new Criteria(
            [],
            Order::desc('priority')
        );
        $events = $servicesApi->get($eventsCriteria)['data'];
        $newEvent = [
            'id' => 0,
            'image' => 'https://picsum.photos/1000/500',
            'content' => [
                [
                    'language_code' => 'es',
                    'service' => '¡No te pierdas nada!',
                    'short_description' => 'Descubre todos los eventos que tenemos para ti'
                ],
                [
                    'language_code' => 'en',
                    'service' => 'Don\'t miss anything!',
                    'short_description' => 'Discover all the events we have for you'
                ]
            ]
        ];

        // create criteria with language_code = 'es'
        $criteria = new Criteria([
            new Filter('lang_code', FilterOperators::EQUAL, $language)
        ]);

        $calendarEvents = $servicesApi->getCalendarEvents($criteria)['data'];

        array_push($events, $newEvent);

        $responseData = [
            'currentPage' => 'home',
            ...$config,
            'lastActivities' => $lastActivities,
            'lastServices' => $lastServices,
            'lastNews' => $lastNews,
            'categories' => $categories,
            'activities' => $activities,
            'events' => $events,
            'calendarEvents' => $calendarEvents
        ];
//var_dump($activities);
        return $view->render($response, 'pages/home.html.twig', $responseData);
    });


    $group->get('/category/{id}[/]', function (Request $request, Response $response, $language, $id) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');

        $defaultLang = $config['defaultLang'];

        // Setting base filter
        $languageFilter = new Filter('lang_code', FilterOperators::IN, [$defaultLang, $language]);

        $activitiesApi = new ActivitiesApi(Container::$client);
        $categoriesApi = new CategoriesApi(Container::$client);

        $activitiesCriteria = (new ActiveActivities())->addFilter($languageFilter);
        [$category] = $categoriesApi->get(new CategoryById($id))['data'];

        $filteredContents = [];
        $activities = $activitiesApi->get($activitiesCriteria->addFilter(new Filter('category_id', FilterOperators::EQUAL, $category['id'])))['data'];
        $contentObj = [];

        foreach ($activities as $activity) {
            $content = TwigUtils::staticGetContentFilteredByLang($activity['content'], 'language_code', $language, $defaultLang);
            $activity['content'] = $content;
            $activity['start_date'] = TimeZoneFormatter::formatTimeZone($activity['start_date'], $_ENV['DATE_FORMAT_PHP']);
            $activity['start_hour'] = TimeZoneFormatter::formatTimeZone($activity['start_hour'], $_ENV['TIME_FORMAT_PHP']);
            $activity['end_hour'] = TimeZoneFormatter::formatTimeZone($activity['end_hour'], $_ENV['TIME_FORMAT_PHP']);
            $contentObj[] = $activity;
        }
        $filteredContents[] = $contentObj;

        $responseData = [
            'currentPage' => 'categories',
            ...$config,
            'activities' => $activities,
            'category' => $category,
            'filteredContents' => $filteredContents,
        ];
        return $view->render($response, 'pages/categories/category.html.twig', $responseData);
    });

    $group->get('/categories[/]', function (Request $request, Response $response, $language) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');

        $defaultLanguage = $config['defaultLang'];

        $categoriesApi = new CategoriesApi(Container::$client);
        $categories = $categoriesApi->get(new ByLangCode([$language, $defaultLanguage]))['data'];

        $responseData = [
            'currentPage' => 'categories',
            ...$config,
            'categories' => $categories,
        ];
        return $view->render($response, 'pages/categories/categories.html.twig', $responseData);
    });

    $group->get('/activities[/]', function (Request $request, Response $response, string $language) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');

        $defaultLang = $config['defaultLang'];

        // Setting base filter
        $languageFilter = new Filter('lang_code', FilterOperators::IN, [$defaultLang, $language]);
        $activitiesApi = new ActivitiesApi(Container::$client);
        $activitiesCriteria = (new ActiveActivities())->addFilter($languageFilter);
        $activities = $activitiesApi->get($activitiesCriteria)['data'];

        $responseData = [
            'currentPage' => 'activities',
            ...$config,
            'activities' => $activities,
        ];

        return $view->render($response, 'pages/activities/activities.html.twig', $responseData);
    });

    $group->get('/activity/{id}[/]', function (Request $request, Response $response, string $language, string $id) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');
        $activitiesApi = new ActivitiesApi(Container::$client);
        $categoriesApi = new CategoriesApi(Container::$client);

        $defaultLanguage = $config['defaultLang'];
        $languageFilter = new Filter('lang_code', FilterOperators::IN, [$defaultLanguage, $language]);
        [$currentActivity] = $activitiesApi->get(new ActivityById((int)$id))['data'];
        $categories = $categoriesApi->get(new Criteria([$languageFilter]))['data'];
        $date = $request->getQueryParams()['date'];

        $responseData = [
            ...$config,
            'currentActivity' => $currentActivity,
            'categories' => $categories,
            'date' => $date
        ];

        return $view->render($response, 'pages/activities/activity.html.twig', $responseData);
    });

    $group->get('/culture[/]', function (Request $request, Response $response, string $language) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');

        $responseData = [
            'currentPage' => 'culture',
            ...$config,
        ];

        return $view->render($response, 'pages/culture.html.twig', $responseData);
    });

    $group->get('/virtual-tour[/]', function (Request $request, Response $response, string $language) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');
        $responseData = [
            'currentPage' => 'virtual-tour',
            ...$config,
        ];
        return $view->render($response, 'pages/virtual_tour.html.twig', $responseData);
    });

    $group->get('/legals[/]', function (Request $request, Response $response, string $language) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');

        $legalsApi = new LegalsApi(Container::$client);
        $data = $legalsApi->get();

        $legalPages = [];

        foreach ($data as $item) {
            $newItem = [
                "slug" => $item["key"],
                "content" => []
            ];

            foreach ($item["translations"] as $translation) {
                $newItem["content"][] = [
                    "language_code" => $translation["language_code"],
                    "title" => $translation["title"]
                ];
            }

            $legalPages[] = $newItem;
        }

        $responseData = [
            'currentPage' => 'legals',
            ...$config,
            'legalPages' => $legalPages,
        ];

        return $view->render($response, 'pages/legals/index.html.twig', $responseData);
    });

    $group->get('/legals/{page}[/]', function (Request $request, Response $response, string $language, string $page) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');

        $legalsApi = new LegalsApi(Container::$client);
        $data = $legalsApi->get();

        $legalContent = [];

        foreach ($data as $item) {
            $slug = $item["key"];
            $legalContent[$slug] = ["content" => []];

            foreach ($item["translations"] as $translation) {
                $legalContent[$slug]["content"][] = [
                    "language_code" => $translation["language_code"],
                    "title" => $translation["title"],
                    "description" => $translation["translation"]
                ];
            }
        }

        // Modificar la ruta /legals para usar el mismo objeto
        if ($page === null) {
            $legalPages = [];
            foreach ($legalContent as $slug => $pageData) {
                $legalPages[] = [
                    'slug' => $slug,
                    'content' => $pageData['content']
                ];
            }

            $responseData = [
                'currentPage' => 'legals',
                ...$config,
                'legalPages' => $legalPages
            ];

            return $view->render($response, 'pages/legals/index.html.twig', $responseData);
        }

        if (!isset($legalContent[$page])) {
            return $response->withStatus(404);
        }

        $responseData = [
            'currentPage' => 'legals',
            ...$config,
            'legal' => $legalContent[$page]
        ];

        return $view->render($response, 'pages/legals/legal_page.html.twig', $responseData);
    });

    $group->get('/events[/]', function (Request $request, Response $response, string $language) {
        $view = Twig::fromRequest($request);
        $serviceApi = new ServicesApi(Container::$client);
        $config = $request->getAttribute('config');

        $categoriesApi = new CategoriesApi(Container::$client);
        $defaultLang = $config['defaultLang'];
        $languageFilter = new Filter('lang_code', FilterOperators::IN, [$defaultLang, $language]);
        // Get all services without criteria
        $services = $serviceApi->get(Criteria::empty())['data'];
        $categories = $categoriesApi->get(new Criteria([$languageFilter]))['data'];
        $criteria = new Criteria([
            new Filter('lang_code', FilterOperators::EQUAL, $language)
        ]);

        $idCriteria =  (new Criteria([]));
        $serviceBookings = $serviceApi->getBookings($idCriteria)['data'];

        $customerPrices = $serviceApi->getServicesWithMinPrice(Criteria::empty())['data'];
        $calendarEvents = $serviceApi->getCalendarEvents($criteria)['data'];

        // var_dump($services);
        $responseData = [
            'env' => $_ENV,
            'currentPage' => 'events',
            ...$config,
            'services' => $services,
            'categories' => $categories,
            'calendarEvents' => $calendarEvents,
            'customerPrices' => $customerPrices,
            'serviceBookings' => $serviceBookings

        ];

        return $view->render($response, 'pages/events/events.html.twig', $responseData);
    });

    $group->get('/events/{id}[/]', function (Request $request, Response $response, string $language, string $id) {
        $view = Twig::fromRequest($request);
        $serviceApi = new ServicesApi(Container::$client);
        $config = $request->getAttribute('config');

        // 🔐 Read query param
        $queryParams = $request->getQueryParams();
        $encryptedKey = $queryParams['key'] ?? null;
        $startSelected = $queryParams['start'] ?? null;
        $endSelected = $queryParams['end'] ?? null;

        // Get specific service by ID
        [$service] = $serviceApi->get(new ServiceById((int)$id))['data'];
        $bookingsReviews = $serviceApi->getBookingsReviews(Criteria::empty())['data'];
        $relatedEvents = $serviceApi->getRelatedServices(Criteria::empty(), (int)$id, $language)['data'];
        $eventTickets = $serviceApi->getServiceCustomerTypesById(Criteria::empty(), (int)$id)['data'];
        $additionalProducts = $serviceApi->getRelatedProductsByLang(Criteria::empty(), (int)$id, $language)['data'];


        $isValidKey = false;
        $relatedDiscount = null;
        $relatedDiscountId = null;
        $relatedDiscountType = null;
        $idCriteria =  (new Criteria([]));
        $serviceBookings = $serviceApi->getBookings($idCriteria)['data'];


        if ($encryptedKey) {
            try {
                $validationResponse = $serviceApi->isValidBookingKey(Criteria::empty(), $encryptedKey)['data'];
                $isValidKey = $validationResponse['isValid'];
                $relatedDiscount = $validationResponse['discount'] ?? null;
                $relatedDiscountType = $validationResponse['type'] ?? null;
                $relatedDiscountId = $validationResponse['id'] ?? null;
            } catch (Throwable $e) {
                $isValidKey = false;
            }
        }

        $discountData = `null`;
        try {
            $discountData = $serviceApi->getDiscountByServiceId(Criteria::empty(), (int)$id)['data'];
            /*echo "<pre>";
            var_dump($discountData);
            echo "</pre>";*/

//             echo (json_encode($discountData)); // todelete
        } catch (Exception $e) {
            if (method_exists($e, 'getCode') && $e->getCode() !== 404) {
                throw $e;
            }
        }

        $responseData = [
            'currentPage' => 'events',
            ...$config,
            'service' => $service,
            'env'=> $_ENV,
            'serviceBookings' => $serviceBookings,
            'additionalProducts' => $additionalProducts,
            'eventTickets' => $eventTickets,
            'bookingsReviews' => $bookingsReviews,
            'relatedEvents' => $relatedEvents,
            'discountData' => $discountData ?? null,
            'isValidKey' => $isValidKey,
            'relatedDiscount' => $relatedDiscount,
            'relatedDiscountType' => $relatedDiscountType,
            'relatedDiscountId' => $relatedDiscountId,
            'startSelected' => $startSelected,
            'endSelected' => $endSelected,
        ];

        return $view->render($response, 'pages/events/event_detail.html.twig', $responseData);
    });


    $group->get('/purchase/{serviceBookingId}/success', function (Request $request, Response $response, string $language, string $serviceBookingId) {
        $view = Twig::fromRequest($request);
        $serviceApi = new ServicesApi(Container::$client);
        $config = $request->getAttribute('config');

        // Get specific service by ID
        $bookingService = $serviceApi->getBookingsById(Criteria::empty(), (int)$serviceBookingId, $language)['data'];
        $relatedEventsData = $serviceApi->getRelatedServicesWithKey(Criteria::empty(), (int)$bookingService['id'], $language)['data'];
        $relatedEvents = $relatedEventsData['data'] ?? null;

        $encryptedKey = $relatedEventsData['encryptedBookingId'] ?? null;
        $validationResponse = $serviceApi->isValidBookingKey(Criteria::empty(), $encryptedKey)['data'];
        $relatedDiscount = $validationResponse['discount'] ?? null;
        $relatedDiscountType = $validationResponse['type'] ?? null;

        $responseData = [
            ...$config,
            'bookingService' => $bookingService,
            'relatedEvents' => $relatedEvents,
            'key' => $relatedEventsData['encryptedBookingId'] ?? '',
            'relatedDiscount' => $relatedDiscount,
            'relatedDiscountType' => $relatedDiscountType
        ];

        return $view->render($response, 'pages/purchase/success.html.twig', $responseData);
    });

    $group->get('/events/booked/{serviceBookingId}', function (Request $request, Response $response, string $language, string $serviceBookingId) {
        $view = Twig::fromRequest($request);
        $serviceApi = new ServicesApi(Container::$client);
        $config = $request->getAttribute('config');

        // Get specific service by ID
        $bookingService = $serviceApi->getBookingsById(Criteria::empty(), (int)$serviceBookingId, $language)['data'];

        $responseData = [
            'currentPage' => 'events',
            ...$config,
            'bookingService' => $bookingService,
        ];

        return $view->render($response, 'pages/events/booked_event.html.twig', $responseData);
    });

    $group->get('/contact[/]', function (Request $request, Response $response, string $language) {
        $view = Twig::fromRequest($request);
        $config = $request->getAttribute('config');

        return $view->render($response, 'pages/contact.html.twig', [
            ...$config,
            'currentPage' => 'contact'
        ]);
    });
})->add(function (Request $request, RequestHandler $handler) {
    $language = explode('/', $request->getUri()->getPath())[1];
    $_GET['lang'] = $language;

    $accessTokenData = $request->getCookieParams()['accessToken'];
    $currentUser = null;
    if ($accessTokenData) {
        /*$accessTokenData =
            "eyJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************.3KDQsH27WFAk_hoTF6TwbIzwRWgC6DwM6vM0bX-AVbY";
        */
        $accessToken = explode(".", $accessTokenData)[1];
        $currentUser = json_decode(base64_decode($accessToken));
    }

    $config = getConfigData(Container::$client, $language);
    $config['currentUser'] = $currentUser;

    $request = $request->withAttribute('config', $config);

    return $handler->handle($request);
});

$app->redirect('/[{path:.*}]', '/en/home/', 301);

$app->run();
